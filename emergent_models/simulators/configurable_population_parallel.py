"""
Configurable population-parallel simulator that supports different encoders.

This module allows users to specify custom encoders for different input representations.
"""

import numpy as np
from typing import Union

try:
    import numba as nb
    NUMBA_AVAILABLE = True
except ImportError:
    NUMBA_AVAILABLE = False
    nb = None

from ..encoders.em43_encoder import <PERSON><PERSON><PERSON><PERSON><PERSON>der, EM43EncoderNumba
from ..encoders.binary_encoder import BinaryEncoder, BinaryEncoderNumba


if NUMBA_AVAILABLE:
    @nb.njit(parallel=True, fastmath=True, cache=True)
    def _simulate_with_encoded_states(
        rules_array: np.ndarray,  # (P, 64)
        initial_states: np.ndarray,  # (P, B, window) - pre-encoded states
        max_steps: int,
        halt_thresh: float
    ) -> np.ndarray:  # (P, B, window) - final states
        """
        Generic simulation that takes pre-encoded states and returns final states.
        This is encoder-agnostic - it just runs CA rules on whatever states it receives.
        """
        P, B, window = initial_states.shape

        # Pre-allocate buffers
        all_states = initial_states.copy()
        all_next_states = np.zeros((P, B, window), dtype=np.uint8)
        all_halted = np.zeros((P, B), dtype=nb.boolean)
        all_frozen = np.zeros((P, B, window), dtype=np.uint8)

        # Simulation loop
        for step in range(max_steps):
            # Clear all next states
            all_next_states.fill(0)

            # Apply CA rules to ALL genome×input combinations in parallel
            for p in nb.prange(P):
                rule = rules_array[p]

                for b in range(B):
                    if all_halted[p, b]:
                        continue

                    # Apply CA rule to this specific genome×input combination
                    for x in range(1, window - 1):
                        left = all_states[p, b, x - 1]
                        center = all_states[p, b, x]
                        right = all_states[p, b, x + 1]
                        idx = (left << 4) | (center << 2) | right
                        all_next_states[p, b, x] = rule[idx]

            # Swap state buffers
            all_states, all_next_states = all_next_states, all_states

            # Check halting conditions
            for p in nb.prange(P):
                for b in range(B):
                    if all_halted[p, b]:
                        continue

                    live_count = 0
                    blue_count = 0
                    for x in range(window):
                        if all_states[p, b, x] != 0:
                            live_count += 1
                            if all_states[p, b, x] == 3:
                                blue_count += 1

                    if live_count > 0 and blue_count / live_count >= halt_thresh:
                        all_halted[p, b] = True
                        # Store final state
                        for x in range(window):
                            all_frozen[p, b, x] = all_states[p, b, x]

        # Return final states (frozen where halted, current where not)
        final_states = np.zeros((P, B, window), dtype=np.uint8)
        for p in nb.prange(P):
            for b in range(B):
                if all_halted[p, b]:
                    for x in range(window):
                        final_states[p, b, x] = all_frozen[p, b, x]
                else:
                    for x in range(window):
                        final_states[p, b, x] = all_states[p, b, x]

        return final_states


def simulate_population_batch_with_encoder(
    rules_array: np.ndarray,
    programmes_array: np.ndarray,
    inputs: np.ndarray,
    window: int,
    max_steps: int,
    halt_thresh: float,
    encoder: Union[EM43Encoder, BinaryEncoder]
) -> np.ndarray:
    """
    Simulate population with configurable encoder.
    
    Args:
        rules_array: Array of CA rules for each genome (P, 64)
        programmes_array: Array of programmes for each genome (P, L)
        inputs: Input values to test (B,)
        window: Size of CA window
        max_steps: Maximum simulation steps
        halt_thresh: Halting threshold for blue cell ratio
        encoder: Encoder instance to use for encoding/decoding
        
    Returns:
        Output values for each genome×input combination (P, B)
    """
    if not NUMBA_AVAILABLE:
        raise RuntimeError("Numba is required for configurable population-parallel simulation!")
    
    # Step 1: Encode using the provided encoder
    if hasattr(encoder, 'encode_batch'):
        # Use batch encoding if available (Numba encoders)
        initial_states = encoder.encode_batch(programmes_array, inputs, window)
    else:
        # Fall back to individual encoding
        P, L = programmes_array.shape
        B = len(inputs)
        initial_states = np.zeros((P, B, window), dtype=np.uint8)
        
        for p in range(P):
            for b in range(B):
                initial_states[p, b] = encoder.encode(programmes_array[p], inputs[b], window)
    
    # Step 2: Simulate (encoder-agnostic)
    final_states = _simulate_with_encoded_states(
        rules_array, initial_states, max_steps, halt_thresh
    )
    
    # Step 3: Decode using the provided encoder
    if hasattr(encoder, 'decode_batch'):
        # Use batch decoding if available (Numba encoders)
        outputs = encoder.decode_batch(final_states, programmes_array.shape[1])
    else:
        # Fall back to individual decoding
        P, B, _ = final_states.shape
        outputs = np.zeros((P, B), dtype=np.int32)
        
        for p in range(P):
            for b in range(B):
                outputs[p, b] = encoder.decode(final_states[p, b], programmes_array.shape[1])
    
    return outputs


def evaluate_population_fitness_with_encoder(
    rules_array: np.ndarray,
    programmes_array: np.ndarray,
    inputs: np.ndarray,
    targets: np.ndarray,
    window: int,
    max_steps: int,
    halt_thresh: float,
    encoder: Union[EM43Encoder, BinaryEncoder],
    sparsity_penalty: float = 0.01,
    use_continuous_fitness: bool = False
) -> np.ndarray:
    """
    Evaluate population fitness with configurable encoder.
    
    Args:
        rules_array: Array of CA rules for each genome (P, 64)
        programmes_array: Array of programmes for each genome (P, L)
        inputs: Input values to test (B,)
        targets: Expected output values (B,)
        window: Size of CA window
        max_steps: Maximum simulation steps
        halt_thresh: Halting threshold for blue cell ratio
        encoder: Encoder instance to use for encoding/decoding
        sparsity_penalty: Penalty coefficient for programme complexity
        use_continuous_fitness: If True, use continuous error-based fitness
        
    Returns:
        Fitness scores for each genome (P,)
    """
    if not NUMBA_AVAILABLE:
        raise RuntimeError("Numba is required for configurable population-parallel fitness evaluation!")
    
    # Get outputs using the configurable encoder
    all_outputs = simulate_population_batch_with_encoder(
        rules_array, programmes_array, inputs, window, max_steps, halt_thresh, encoder
    )
    
    # Calculate fitness (same logic regardless of encoder)
    P = rules_array.shape[0]
    L = programmes_array.shape[1]
    B = inputs.shape[0]
    
    fitness = np.empty(P, dtype=np.float32)
    
    for p in range(P):
        programme = programmes_array[p]
        genome_outputs = all_outputs[p]
        
        if use_continuous_fitness:
            total_error = 0.0
            for j in range(B):
                total_error += abs(genome_outputs[j] - targets[j])
            avg_error = total_error / B
            accuracy_score = -avg_error
        else:
            correct = 0
            for j in range(B):
                if genome_outputs[j] == targets[j]:
                    correct += 1
            accuracy_score = correct / B
        
        # Add sparsity penalty
        sparsity = np.count_nonzero(programme) / L
        sparsity_pen = sparsity_penalty * sparsity
        
        fitness[p] = accuracy_score - sparsity_pen
    
    return fitness
