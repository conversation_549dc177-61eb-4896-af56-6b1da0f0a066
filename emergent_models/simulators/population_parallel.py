"""
Population-parallel cellular automata simulation using Numba.

This module provides high-performance population-parallel evaluation of CA genomes
using Numba JIT compilation for maximum speed.
"""

import numpy as np

try:
    import numba as nb
    NUMBA_AVAILABLE = True
except ImportError:
    NUMBA_AVAILABLE = False
    nb = None


if NUMBA_AVAILABLE:
    @nb.njit(parallel=True, fastmath=True, cache=True)
    def _simulate_population_batch_optimized(
        rules_array: np.ndarray,  # (P, 64)
        programmes_array: np.ndarray,  # (P, L)
        inputs: np.ndarray,  # (B,)
        window: int,
        max_steps: int,
        halt_thresh: float
    ) -> np.ndarray:  # (P, B) - outputs for each genome×input combination
        """
        ULTIMATE OPTIMIZATION: Process entire population × inputs in one massive parallel kernel.
        This is the key to matching standalone performance - true (P×B) batch processing.
        
        Args:
            rules_array: Array of CA rules for each genome (P, 64)
            programmes_array: Array of programmes for each genome (P, L)
            inputs: Input values to test (B,)
            window: Size of CA window
            max_steps: Maximum simulation steps
            halt_thresh: Halting threshold for blue cell ratio
            
        Returns:
            Output values for each genome×input combination (P, B)
        """
        P = rules_array.shape[0]  # Population size
        L = programmes_array.shape[1]  # Programme length
        B = inputs.shape[0]  # Batch size (number of inputs)

        # Pre-allocate ALL buffers for maximum performance
        # Shape: (P, B, window) - every genome×input combination gets its own state
        all_states = np.zeros((P, B, window), dtype=np.uint8)
        all_next_states = np.zeros((P, B, window), dtype=np.uint8)
        all_halted = np.zeros((P, B), dtype=nb.boolean)
        all_frozen = np.zeros((P, B, window), dtype=np.uint8)
        outputs = np.zeros((P, B), dtype=np.int32)

        # Initialize all genome×input combinations in parallel
        for p in nb.prange(P):
            rule = rules_array[p]
            programme = programmes_array[p]

            for b in range(B):
                input_val = inputs[b]

                # Write programme
                for j in range(L):
                    all_states[p, b, j] = programme[j]

                # Write separator (BB)
                all_states[p, b, L] = 3      # Blue
                all_states[p, b, L + 1] = 3  # Blue

                # Write input as positional encoding
                beacon_pos = L + 2 + input_val + 1
                if beacon_pos < window:
                    all_states[p, b, beacon_pos] = 2  # Red beacon

        # Massive parallel simulation - all P×B combinations evolve simultaneously
        for step in range(max_steps):
            # Clear all next states
            all_next_states.fill(0)

            # Apply CA rules to ALL genome×input combinations in parallel
            for p in nb.prange(P):
                rule = rules_array[p]

                for b in range(B):
                    if all_halted[p, b]:
                        continue

                    # Apply CA rule to this specific genome×input combination
                    for x in range(1, window - 1):
                        left = all_states[p, b, x - 1]
                        center = all_states[p, b, x]
                        right = all_states[p, b, x + 1]
                        idx = (left << 4) | (center << 2) | right
                        all_next_states[p, b, x] = rule[idx]

            # Swap state buffers (all at once)
            all_states, all_next_states = all_next_states, all_states

            # Check halting conditions for all combinations in parallel
            for p in nb.prange(P):
                for b in range(B):
                    if all_halted[p, b]:
                        continue

                    live_count = 0
                    blue_count = 0
                    for x in range(window):
                        if all_states[p, b, x] != 0:
                            live_count += 1
                            if all_states[p, b, x] == 3:
                                blue_count += 1

                    if live_count > 0 and blue_count / live_count >= halt_thresh:
                        all_halted[p, b] = True
                        # Store final state
                        for x in range(window):
                            all_frozen[p, b, x] = all_states[p, b, x]

        # Decode all outputs in parallel
        for p in nb.prange(P):
            for b in range(B):
                if all_halted[p, b]:
                    # Find rightmost red in frozen state
                    for x in range(window - 1, -1, -1):
                        if all_frozen[p, b, x] == 2:
                            outputs[p, b] = x - (L + 3)
                            break

        return outputs


def simulate_population_batch(
    rules_array: np.ndarray,
    programmes_array: np.ndarray,
    inputs: np.ndarray,
    window: int,
    max_steps: int,
    halt_thresh: float
) -> np.ndarray:
    """
    Simulate entire population on batch of inputs with maximum performance.
    
    Args:
        rules_array: Array of CA rules for each genome (P, 64)
        programmes_array: Array of programmes for each genome (P, L)
        inputs: Input values to test (B,)
        window: Size of CA window
        max_steps: Maximum simulation steps
        halt_thresh: Halting threshold for blue cell ratio
        
    Returns:
        Output values for each genome×input combination (P, B)
        
    Raises:
        RuntimeError: If Numba is not available
    """
    if not NUMBA_AVAILABLE:
        raise RuntimeError(
            "Numba is required for population-parallel simulation! "
            "Please install numba: pip install numba"
        )
    
    return _simulate_population_batch_optimized(
        rules_array, programmes_array, inputs, window, max_steps, halt_thresh
    )


if NUMBA_AVAILABLE:
    @nb.njit(parallel=True, fastmath=True, cache=True)
    def _evaluate_population_parallel_fitness(
        rules_array: np.ndarray,
        programmes_array: np.ndarray,
        inputs: np.ndarray,
        targets: np.ndarray,
        window: int,
        max_steps: int,
        halt_thresh: float,
        sparsity_penalty: float,
        use_continuous_fitness: bool
    ) -> np.ndarray:
        """
        ULTIMATE OPTIMIZATION: Single massive kernel for entire population×inputs.
        This matches the standalone version's architecture for maximum performance.

        Args:
            rules_array: Array of CA rules for each genome (P, 64)
            programmes_array: Array of programmes for each genome (P, L)
            inputs: Input values to test (B,)
            targets: Expected output values (B,)
            window: Size of CA window
            max_steps: Maximum simulation steps
            halt_thresh: Halting threshold for blue cell ratio
            sparsity_penalty: Penalty coefficient for programme complexity
            use_continuous_fitness: If True, use continuous error-based fitness

        Returns:
            Fitness scores for each genome (P,)
        """
        P = rules_array.shape[0]  # Population size
        L = programmes_array.shape[1]  # Programme length
        B = inputs.shape[0]  # Number of inputs

        # Get all outputs in one massive parallel call
        all_outputs = _simulate_population_batch_optimized(
            rules_array, programmes_array, inputs, window, max_steps, halt_thresh
        )  # Shape: (P, B)

        fitness = np.empty(P, dtype=np.float32)

        # Calculate fitness for each genome in parallel
        for p in nb.prange(P):
            programme = programmes_array[p]

            # Extract outputs for this genome
            genome_outputs = all_outputs[p]  # Shape: (B,)

            # Calculate fitness for this genome
            if use_continuous_fitness:
                # Continuous fitness: mean absolute error (like standalone version)
                total_error = 0.0
                for j in range(B):
                    total_error += abs(genome_outputs[j] - targets[j])

                avg_error = total_error / B
                accuracy_score = -avg_error  # Negative error (higher is better)
            else:
                # Binary accuracy fitness - only count exact matches
                correct = 0
                for j in range(B):
                    if genome_outputs[j] == targets[j]:
                        correct += 1
                accuracy_score = correct / B

            # Add sparsity penalty
            sparsity = 0
            for k in range(L):
                if programme[k] != 0:
                    sparsity += 1
            sparsity_pen = sparsity_penalty * sparsity / L

            fitness[p] = accuracy_score - sparsity_pen

        return fitness


def evaluate_population_fitness(
    rules_array: np.ndarray,
    programmes_array: np.ndarray,
    inputs: np.ndarray,
    targets: np.ndarray,
    window: int,
    max_steps: int,
    halt_thresh: float,
    sparsity_penalty: float = 0.01,
    use_continuous_fitness: bool = False
) -> np.ndarray:
    """
    Evaluate fitness for entire population with maximum performance.

    Args:
        rules_array: Array of CA rules for each genome (P, 64)
        programmes_array: Array of programmes for each genome (P, L)
        inputs: Input values to test (B,)
        targets: Expected output values (B,)
        window: Size of CA window
        max_steps: Maximum simulation steps
        halt_thresh: Halting threshold for blue cell ratio
        sparsity_penalty: Penalty coefficient for programme complexity
        use_continuous_fitness: If True, use continuous error-based fitness

    Returns:
        Fitness scores for each genome (P,)

    Raises:
        RuntimeError: If Numba is not available
    """
    if not NUMBA_AVAILABLE:
        raise RuntimeError(
            "Numba is required for population-parallel fitness evaluation! "
            "Please install numba: pip install numba"
        )

    return _evaluate_population_parallel_fitness(
        rules_array, programmes_array, inputs, targets,
        window, max_steps, halt_thresh, sparsity_penalty, use_continuous_fitness
    )


def check_numba_availability() -> bool:
    """
    Check if Numba is available for population-parallel simulation.

    Returns:
        True if Numba is available, False otherwise
    """
    return NUMBA_AVAILABLE
