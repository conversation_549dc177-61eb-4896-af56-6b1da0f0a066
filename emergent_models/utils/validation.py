"""
Validation utilities for cellular automata simulations.

This module provides validation functions for CA parameters and simulation states.
"""

import numpy as np


def meets_halt_condition(live_count: int, blue_count: int, halt_thresh: float) -> bool:
    """
    Check if halting condition is met with proper error handling.
    
    Args:
        live_count: Number of live (non-zero) cells
        blue_count: Number of blue cells (value 3)
        halt_thresh: Threshold for blue cell ratio to trigger halting
        
    Returns:
        True if halting condition is met, False otherwise
    """
    if live_count <= 0:
        return False
    return blue_count / live_count >= halt_thresh


def validate_input_range(input_range: tuple, window: int, prog_length: int) -> None:
    """
    Validate that input range fits within window constraints.
    
    For EM-4/3 encoding format: [programme][BB][0^(input+1)][R][0...]
    
    Args:
        input_range: Tuple of (start, end) input values
        window: Size of the CA window
        prog_length: Length of the programme
        
    Raises:
        ValueError: If the maximum input value would place the beacon outside the window
    """
    max_input = input_range[1]
    # Format: [programme][BB][0^(input+1)][R][0...]
    max_beacon_pos = prog_length + 2 + max_input + 1
    if max_beacon_pos >= window:
        raise ValueError(
            f"Maximum input {max_input} requires beacon at position {max_beacon_pos}, "
            f"but window size is only {window}. "
            f"Increase window size to at least {max_beacon_pos + 10} or reduce input range."
        )


def validate_ca_parameters(window: int, max_steps: int, halt_thresh: float, 
                          programme_length: int) -> None:
    """
    Validate cellular automata simulation parameters.
    
    Args:
        window: Size of the CA window
        max_steps: Maximum number of simulation steps
        halt_thresh: Halting threshold (0.0 to 1.0)
        programme_length: Length of the programme
        
    Raises:
        ValueError: If any parameter is invalid
    """
    if window <= 0:
        raise ValueError(f"Window size must be positive, got {window}")
    
    if max_steps <= 0:
        raise ValueError(f"Max steps must be positive, got {max_steps}")
    
    if not 0.0 <= halt_thresh <= 1.0:
        raise ValueError(f"Halt threshold must be between 0.0 and 1.0, got {halt_thresh}")
    
    if programme_length <= 0:
        raise ValueError(f"Programme length must be positive, got {programme_length}")
    
    if programme_length >= window - 10:  # Leave some space for input encoding
        raise ValueError(
            f"Programme length {programme_length} is too large for window size {window}. "
            f"Programme should be at most {window - 10} to leave space for input encoding."
        )
