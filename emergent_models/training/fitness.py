"""
Fitness function factories for cellular automata training.

This module provides utilities to create fitness functions for different tasks
and evaluation strategies, including population-parallel evaluation.
"""

import numpy as np
from typing import Callable, List, Tuple, Union
import traceback

from ..simulators.numba_simulator import NumbaSimulator
from ..simulators.population_parallel import (
    evaluate_population_fitness, 
    check_numba_availability
)
from ..rules.sanitization import sanitize_rule, sanitize_programme
from ..rules.em43 import EM43Genome


def create_population_parallel_fitness_function(
    simulator: NumbaSimulator,
    input_range: Tuple[int, int] = (1, 30),
    use_continuous_fitness: bool = False,
    sparsity_penalty: float = 0.01,
    task_type: str = "doubling"
) -> Callable:
    """
    Create a population-parallel fitness function for maximum speed.
    
    Args:
        simulator: NumbaSimulator instance for parameters
        input_range: Tuple of (start, end) input values
        use_continuous_fitness: If True, use continuous error-based fitness
        sparsity_penalty: Penalty coefficient for programme complexity
        task_type: Type of task ("doubling", "gcd", "lcm")
        
    Returns:
        Population fitness function that takes a list of genomes and returns fitness scores
        
    Raises:
        RuntimeError: If <PERSON><PERSON> is not available
    """
    if not check_numba_availability():
        raise RuntimeError(
            "Numba is required for optimized performance! "
            "Please install numba: pip install numba"
        )

    # Generate inputs and targets based on task type
    inputs = np.array(list(range(input_range[0], input_range[1] + 1)), dtype=np.int64)
    
    if task_type == "doubling":
        targets = np.array([2 * x for x in inputs], dtype=np.int64)
    elif task_type == "gcd":
        # For GCD, we need pairs of inputs - this is a simplified version
        # In practice, you'd want to handle this differently
        targets = np.array([1 for _ in inputs], dtype=np.int64)  # Placeholder
    elif task_type == "lcm":
        # For LCM, we need pairs of inputs - this is a simplified version
        targets = np.array([x * x for x in inputs], dtype=np.int64)  # Placeholder
    else:
        raise ValueError(f"Unknown task type: {task_type}")

    def population_fitness_fn(population: List[EM43Genome]) -> List[float]:
        """
        Evaluate entire population at once using population-parallel evaluation.
        
        Args:
            population: List of EM43Genome instances
            
        Returns:
            List of fitness scores for each genome
        """
        if not population:
            return []

        # Extract rules and programmes from population
        pop_size = len(population)
        prog_length = len(population[0].programme)

        rules_array = np.zeros((pop_size, 64), dtype=np.uint8)
        programmes_array = np.zeros((pop_size, prog_length), dtype=np.uint8)

        for i, genome in enumerate(population):
            # CRITICAL: Apply sanitization like standalone version
            rules_array[i] = sanitize_rule(genome.rule.get_rule_array())
            programmes_array[i] = sanitize_programme(genome.programme)

        # Use population-parallel evaluation
        fitness_scores = evaluate_population_fitness(
            rules_array, programmes_array, inputs, targets,
            simulator.window, simulator.max_steps, simulator.halt_thresh,
            sparsity_penalty, use_continuous_fitness
        )

        return fitness_scores.tolist()

    return population_fitness_fn


def create_individual_fitness_function(
    simulator: NumbaSimulator, 
    input_range: Tuple[int, int] = (1, 30),
    task_type: str = "doubling",
    sparsity_penalty: float = 0.01
) -> Callable:
    """
    Create a fitness function for individual genome evaluation.
    
    Args:
        simulator: NumbaSimulator instance
        input_range: Tuple of (start, end) input values
        task_type: Type of task ("doubling", "gcd", "lcm")
        sparsity_penalty: Penalty coefficient for programme complexity
        
    Returns:
        Fitness function that takes a genome and simulator and returns fitness score
    """
    inputs = list(range(input_range[0], input_range[1] + 1))
    
    if task_type == "doubling":
        targets = [2 * x for x in inputs]
    elif task_type == "gcd":
        targets = [1 for _ in inputs]  # Placeholder
    elif task_type == "lcm":
        targets = [x * x for x in inputs]  # Placeholder
    else:
        raise ValueError(f"Unknown task type: {task_type}")
    
    def fitness_fn(genome: EM43Genome, sim: NumbaSimulator) -> float:
        """
        Evaluate genome fitness on the specified task.
        
        Args:
            genome: EM43Genome to evaluate
            sim: NumbaSimulator instance
            
        Returns:
            Fitness score (higher is better)
        """
        try:
            outputs = sim.simulate_batch(genome, inputs)
            
            # Calculate accuracy
            correct = 0
            total = len(inputs)
            
            for i, (target, output) in enumerate(zip(targets, outputs)):
                if output >= 0 and output == target:
                    correct += 1
            
            accuracy = correct / total
            
            # Add sparsity penalty (encourage simpler programmes)
            sparsity_pen = sparsity_penalty * np.count_nonzero(genome.programme) / len(genome.programme)
            
            # Fitness is accuracy minus sparsity penalty
            fitness = accuracy - sparsity_pen
            
            return fitness
            
        except Exception as e:
            # Better error handling with more context
            print(f"Error evaluating genome: {e}")
            print(f"Genome programme: {genome.programme}")
            print(f"Traceback: {traceback.format_exc()}")
            return -1000.0  # Large penalty for errors
    
    return fitness_fn


def create_doubling_fitness_function(
    simulator: NumbaSimulator,
    input_range: Tuple[int, int] = (1, 30),
    use_population_parallel: bool = True,
    **kwargs
) -> Callable:
    """
    Create a fitness function specifically for the doubling task.
    
    Args:
        simulator: NumbaSimulator instance
        input_range: Tuple of (start, end) input values
        use_population_parallel: If True, create population-parallel function
        **kwargs: Additional arguments passed to fitness function factory
        
    Returns:
        Appropriate fitness function for the doubling task
    """
    if use_population_parallel and check_numba_availability():
        return create_population_parallel_fitness_function(
            simulator, input_range, task_type="doubling", **kwargs
        )
    else:
        return create_individual_fitness_function(
            simulator, input_range, task_type="doubling", **kwargs
        )


def create_gcd_fitness_function(
    simulator: NumbaSimulator,
    input_range: Tuple[int, int] = (1, 30),
    use_population_parallel: bool = True,
    **kwargs
) -> Callable:
    """
    Create a fitness function for the GCD task.
    
    Note: This is a placeholder implementation. Full GCD task requires
    handling pairs of inputs.
    
    Args:
        simulator: NumbaSimulator instance
        input_range: Tuple of (start, end) input values
        use_population_parallel: If True, create population-parallel function
        **kwargs: Additional arguments passed to fitness function factory
        
    Returns:
        Appropriate fitness function for the GCD task
    """
    if use_population_parallel and check_numba_availability():
        return create_population_parallel_fitness_function(
            simulator, input_range, task_type="gcd", **kwargs
        )
    else:
        return create_individual_fitness_function(
            simulator, input_range, task_type="gcd", **kwargs
        )


def create_lcm_fitness_function(
    simulator: NumbaSimulator,
    input_range: Tuple[int, int] = (1, 30),
    use_population_parallel: bool = True,
    **kwargs
) -> Callable:
    """
    Create a fitness function for the LCM task.
    
    Note: This is a placeholder implementation. Full LCM task requires
    handling pairs of inputs.
    
    Args:
        simulator: NumbaSimulator instance
        input_range: Tuple of (start, end) input values
        use_population_parallel: If True, create population-parallel function
        **kwargs: Additional arguments passed to fitness function factory
        
    Returns:
        Appropriate fitness function for the LCM task
    """
    if use_population_parallel and check_numba_availability():
        return create_population_parallel_fitness_function(
            simulator, input_range, task_type="lcm", **kwargs
        )
    else:
        return create_individual_fitness_function(
            simulator, input_range, task_type="lcm", **kwargs
        )
