"""
Configurable fitness functions that support custom encoders.

This module allows developers to easily use different encoders in their training.
"""

import numpy as np
from typing import Callable, List, Tuple, Union
import traceback

from ..simulators.numba_simulator import NumbaSimulator
from ..simulators.configurable_population_parallel import (
    evaluate_population_fitness_with_encoder,
    simulate_population_batch_with_encoder
)
from ..simulators.population_parallel import check_numba_availability
from ..rules.sanitization import sanitize_rule, sanitize_programme
from ..rules.em43 import EM43Genome
from ..encoders.em43_encoder import EM43Encoder, get_encoder as get_em43_encoder
from ..encoders.binary_encoder import BinaryEncoder, get_binary_encoder


def create_configurable_fitness_function(
    simulator: NumbaSimulator,
    encoder: Union[EM43Encoder, BinaryEncoder],
    input_range: Tuple[int, int] = (1, 30),
    use_continuous_fitness: bool = False,
    sparsity_penalty: float = 0.01,
    task_type: str = "doubling"
) -> Callable:
    """
    Create a fitness function with configurable encoder.
    
    Args:
        simulator: NumbaSimulator instance for parameters
        encoder: Encoder instance to use (EM43Encoder, BinaryEncoder, or custom)
        input_range: Tuple of (start, end) input values
        use_continuous_fitness: If True, use continuous error-based fitness
        sparsity_penalty: Penalty coefficient for programme complexity
        task_type: Type of task ("doubling", "gcd", "lcm")
        
    Returns:
        Population fitness function that takes a list of genomes and returns fitness scores
    """
    if not check_numba_availability():
        raise RuntimeError("Numba is required for configurable fitness functions!")

    # Generate inputs and targets based on task type
    inputs = np.array(list(range(input_range[0], input_range[1] + 1)), dtype=np.int64)
    
    if task_type == "doubling":
        targets = np.array([2 * x for x in inputs], dtype=np.int64)
    elif task_type == "gcd":
        targets = np.array([1 for _ in inputs], dtype=np.int64)  # Placeholder
    elif task_type == "lcm":
        targets = np.array([x * x for x in inputs], dtype=np.int64)  # Placeholder
    else:
        raise ValueError(f"Unknown task type: {task_type}")

    def population_fitness_fn(population: List[EM43Genome]) -> List[float]:
        """
        Evaluate entire population using the configured encoder.
        
        Args:
            population: List of EM43Genome instances
            
        Returns:
            List of fitness scores for each genome
        """
        if not population:
            return []

        # Extract rules and programmes from population
        pop_size = len(population)
        prog_length = len(population[0].programme)

        rules_array = np.zeros((pop_size, 64), dtype=np.uint8)
        programmes_array = np.zeros((pop_size, prog_length), dtype=np.uint8)

        for i, genome in enumerate(population):
            # Apply sanitization
            rules_array[i] = sanitize_rule(genome.rule.get_rule_array())
            programmes_array[i] = sanitize_programme(genome.programme)

        # Use configurable encoder for evaluation
        fitness_scores = evaluate_population_fitness_with_encoder(
            rules_array, programmes_array, inputs, targets,
            simulator.window, simulator.max_steps, simulator.halt_thresh,
            encoder, sparsity_penalty, use_continuous_fitness
        )

        return fitness_scores.tolist()

    return population_fitness_fn


def create_em43_fitness_function(
    simulator: NumbaSimulator,
    input_range: Tuple[int, int] = (1, 30),
    **kwargs
) -> Callable:
    """
    Create fitness function with EM-4/3 positional encoder.
    
    Args:
        simulator: NumbaSimulator instance
        input_range: Tuple of (start, end) input values
        **kwargs: Additional arguments passed to create_configurable_fitness_function
        
    Returns:
        Fitness function using EM-4/3 positional encoding
    """
    encoder = get_em43_encoder(use_numba=True)
    return create_configurable_fitness_function(
        simulator, encoder, input_range, **kwargs
    )


def create_binary_fitness_function(
    simulator: NumbaSimulator,
    input_range: Tuple[int, int] = (1, 30),
    input_state: int = 2,
    max_bits: int = 8,
    **kwargs
) -> Callable:
    """
    Create fitness function with binary encoder.
    
    Args:
        simulator: NumbaSimulator instance
        input_range: Tuple of (start, end) input values
        input_state: State value to use for binary 1's (default: 2 for red)
        max_bits: Maximum number of bits for binary representation
        **kwargs: Additional arguments passed to create_configurable_fitness_function
        
    Returns:
        Fitness function using binary encoding
    """
    encoder = get_binary_encoder(input_state=input_state, max_bits=max_bits, use_numba=True)
    return create_configurable_fitness_function(
        simulator, encoder, input_range, **kwargs
    )


def create_custom_encoder_fitness_function(
    simulator: NumbaSimulator,
    encoder_class,
    encoder_kwargs: dict = None,
    input_range: Tuple[int, int] = (1, 30),
    **kwargs
) -> Callable:
    """
    Create fitness function with custom encoder.
    
    Args:
        simulator: NumbaSimulator instance
        encoder_class: Custom encoder class (must have encode/decode methods)
        encoder_kwargs: Keyword arguments for encoder initialization
        input_range: Tuple of (start, end) input values
        **kwargs: Additional arguments passed to create_configurable_fitness_function
        
    Returns:
        Fitness function using custom encoder
    """
    if encoder_kwargs is None:
        encoder_kwargs = {}
    
    encoder = encoder_class(**encoder_kwargs)
    return create_configurable_fitness_function(
        simulator, encoder, input_range, **kwargs
    )


# Convenience functions for common encoding strategies
def create_doubling_fitness_with_encoder(
    simulator: NumbaSimulator,
    encoder_type: str = "em43",
    input_range: Tuple[int, int] = (1, 30),
    **kwargs
) -> Callable:
    """
    Create doubling task fitness function with specified encoder.
    
    Args:
        simulator: NumbaSimulator instance
        encoder_type: Type of encoder ("em43", "binary", or "custom")
        input_range: Tuple of (start, end) input values
        **kwargs: Additional arguments for encoder and fitness function
        
    Returns:
        Fitness function for doubling task with specified encoder
    """
    if encoder_type == "em43":
        return create_em43_fitness_function(
            simulator, input_range, task_type="doubling", **kwargs
        )
    elif encoder_type == "binary":
        return create_binary_fitness_function(
            simulator, input_range, task_type="doubling", **kwargs
        )
    elif encoder_type == "custom":
        encoder_class = kwargs.pop("encoder_class")
        encoder_kwargs = kwargs.pop("encoder_kwargs", {})
        return create_custom_encoder_fitness_function(
            simulator, encoder_class, encoder_kwargs, input_range, 
            task_type="doubling", **kwargs
        )
    else:
        raise ValueError(f"Unknown encoder type: {encoder_type}")


# Example of how to create a completely custom encoder
class ExampleCustomEncoder:
    """
    Example custom encoder that demonstrates the interface.
    
    This encoder uses a simple offset-based encoding where the input
    value determines the position of a marker cell.
    """
    
    def __init__(self, marker_state: int = 1, offset: int = 5):
        """
        Initialize custom encoder.
        
        Args:
            marker_state: State value for the marker
            offset: Offset to add to input position
        """
        self.marker_state = marker_state
        self.offset = offset
    
    def encode(self, programme: np.ndarray, input_val: int, window: int) -> np.ndarray:
        """Encode programme and input."""
        state = np.zeros(window, dtype=np.uint8)
        L = len(programme)
        
        # Write programme
        state[:L] = programme
        
        # Write separator
        if L < window:
            state[L] = 3  # Blue separator
        
        # Write marker at offset position
        marker_pos = L + 1 + self.offset + input_val
        if marker_pos < window:
            state[marker_pos] = self.marker_state
        
        return state
    
    def decode(self, final_state: np.ndarray, programme_length: int) -> int:
        """Decode final state to output."""
        # Find marker and calculate output
        for i, cell in enumerate(final_state):
            if cell == self.marker_state:
                return i - (programme_length + 1 + self.offset)
        return -10  # Error value
