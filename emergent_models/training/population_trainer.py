"""
Population-parallel training utilities for cellular automata.

This module provides custom training loops optimized for population-parallel
fitness evaluation with maximum performance.
"""

import time
import numpy as np
from typing import Dict, Callable, Optional, Any
from tqdm import tqdm

from ..optimizers.genetic import GAOptimizer
from ..simulators.numba_simulator import NumbaSimulator


class PopulationTrainer:
    """
    Custom trainer for population-parallel fitness evaluation.
    
    This trainer is optimized for maximum performance when using population-parallel
    evaluation strategies that process entire populations at once.
    """
    
    def __init__(self, optimizer: GAOptimizer):
        """
        Initialize the population trainer.
        
        Args:
            optimizer: Genetic algorithm optimizer instance
        """
        self.optimizer = optimizer
        self.best_genome = None
        self.best_fitness = float('-inf')
        self.history = {
            'best_fitness': [],
            'mean_fitness': [],
            'std_fitness': [],
            'generation_times': []
        }
    
    def train(
        self,
        population_fitness_fn: Callable,
        validation_fn: Optional[Callable] = None,
        generations: int = 100,
        early_stopping_threshold: float = 1.0,
        disable_early_stopping: bool = False,
        checkpoint_every: int = 50,
        verbose: bool = True
    ) -> Dict[str, Any]:
        """
        Train using population-parallel fitness evaluation.
        
        Args:
            population_fitness_fn: Function that takes population and returns fitness scores
            validation_fn: Optional validation function for early stopping
            generations: Number of generations to train
            early_stopping_threshold: Accuracy threshold for early stopping
            disable_early_stopping: If True, disable early stopping
            checkpoint_every: Frequency of checkpoint logging
            verbose: If True, show progress bar and logging
            
        Returns:
            Dictionary containing training results and history
        """
        if verbose:
            pbar = tqdm(range(generations), desc="Training")
        else:
            pbar = range(generations)

        for generation in pbar:
            gen_start_time = time.time()

            # Evaluate entire population at once
            fitness_scores = population_fitness_fn(self.optimizer.population)

            # Update best genome
            max_fitness_idx = np.argmax(fitness_scores)
            if fitness_scores[max_fitness_idx] > self.best_fitness:
                self.best_fitness = fitness_scores[max_fitness_idx]
                self.best_genome = self.optimizer.population[max_fitness_idx]
                if hasattr(self.best_genome, 'clone'):
                    self.best_genome = self.best_genome.clone()
                self.best_genome.fitness = self.best_fitness

            # Record history
            self.history['best_fitness'].append(np.max(fitness_scores))
            self.history['mean_fitness'].append(np.mean(fitness_scores))
            self.history['std_fitness'].append(np.std(fitness_scores))

            # Evolution step
            self.optimizer.step(fitness_scores)

            gen_time = time.time() - gen_start_time
            self.history['generation_times'].append(gen_time)

            # Update progress bar
            if verbose and hasattr(pbar, 'set_postfix'):
                pbar.set_postfix({
                    'best': f"{self.best_fitness:.4f}",
                    'mean': f"{np.mean(fitness_scores):.4f}",
                    'time': f"{gen_time:.2f}s"
                })

            # Early stopping check
            if not disable_early_stopping and validation_fn:
                # Create a dummy simulator for validation
                dummy_sim = NumbaSimulator(window=200, max_steps=800, halt_thresh=0.50)
                validation_accuracy = validation_fn(self.best_genome, dummy_sim)
                if validation_accuracy >= early_stopping_threshold:
                    if verbose:
                        print(f"\nEarly stopping at generation {generation + 1}: "
                              f"validation accuracy {validation_accuracy:.1%} >= {early_stopping_threshold:.1%}")
                    break

            # Checkpoint logging
            if verbose and (generation + 1) % checkpoint_every == 0:
                print(f"\nGeneration {generation + 1}: best={self.best_fitness:.4f}, "
                      f"mean={np.mean(fitness_scores):.4f}, time={gen_time:.2f}s")

        return {
            'best_genome': self.best_genome,
            'best_fitness': self.best_fitness,
            'history': self.history,
            'generations_completed': generation + 1
        }
    
    def get_training_stats(self) -> Dict[str, Any]:
        """
        Get training statistics.
        
        Returns:
            Dictionary containing training statistics
        """
        if not self.history['best_fitness']:
            return {}
        
        return {
            'generations': len(self.history['best_fitness']),
            'best_fitness': max(self.history['best_fitness']),
            'final_mean_fitness': self.history['mean_fitness'][-1] if self.history['mean_fitness'] else 0.0,
            'avg_generation_time': np.mean(self.history['generation_times']) if self.history['generation_times'] else 0.0,
            'total_training_time': sum(self.history['generation_times']) if self.history['generation_times'] else 0.0
        }


def train_with_population_parallel(
    optimizer: GAOptimizer,
    population_fitness_fn: Callable,
    validation_fn: Optional[Callable] = None,
    config: Dict[str, Any] = None
) -> tuple:
    """
    Legacy function for backward compatibility.
    
    Train using population-parallel fitness evaluation with the original interface.
    
    Args:
        optimizer: Genetic algorithm optimizer
        population_fitness_fn: Function that takes population and returns fitness scores
        validation_fn: Optional validation function for early stopping
        config: Configuration dictionary
        
    Returns:
        Tuple of (best_genome, history)
    """
    if config is None:
        config = {}
    
    trainer = PopulationTrainer(optimizer)
    
    results = trainer.train(
        population_fitness_fn=population_fitness_fn,
        validation_fn=validation_fn,
        generations=config.get('generations', 100),
        early_stopping_threshold=config.get('early_stopping_threshold', 1.0),
        disable_early_stopping=config.get('disable_early_stopping', False),
        checkpoint_every=config.get('checkpoint_every', 50),
        verbose=True
    )
    
    return results['best_genome'], results['history']


# Note: MockTrainer class removed - no longer needed with cleaner interface
