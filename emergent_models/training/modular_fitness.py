"""
Modular fitness function factories using separate encoder/decoder.

This module provides the same fitness functions as fitness.py but uses
the modular encoder/decoder approach for better separation of concerns.
"""

import numpy as np
from typing import Callable, List, Tuple
import traceback

from ..simulators.numba_simulator import NumbaSimulator
from ..simulators.modular_population_parallel import (
    evaluate_population_fitness_modular,
    simulate_population_batch_modular
)
from ..simulators.population_parallel import check_numba_availability
from ..rules.sanitization import sanitize_rule, sanitize_programme
from ..rules.em43 import EM43<PERSON>enome


def create_modular_population_parallel_fitness_function(
    simulator: NumbaSimulator,
    input_range: Tuple[int, int] = (1, 30),
    use_continuous_fitness: bool = False,
    sparsity_penalty: float = 0.01,
    task_type: str = "doubling"
) -> Callable:
    """
    Create a modular population-parallel fitness function.
    
    This version uses separate encoder/decoder modules for better modularity.
    
    Args:
        simulator: NumbaSimulator instance for parameters
        input_range: Tuple of (start, end) input values
        use_continuous_fitness: If True, use continuous error-based fitness
        sparsity_penalty: Penalty coefficient for programme complexity
        task_type: Type of task ("doubling", "gcd", "lcm")
        
    Returns:
        Population fitness function that takes a list of genomes and returns fitness scores
    """
    if not check_numba_availability():
        raise RuntimeError("Numba is required for modular population-parallel fitness!")

    # Generate inputs and targets based on task type
    inputs = np.array(list(range(input_range[0], input_range[1] + 1)), dtype=np.int64)
    
    if task_type == "doubling":
        targets = np.array([2 * x for x in inputs], dtype=np.int64)
    elif task_type == "gcd":
        targets = np.array([1 for _ in inputs], dtype=np.int64)  # Placeholder
    elif task_type == "lcm":
        targets = np.array([x * x for x in inputs], dtype=np.int64)  # Placeholder
    else:
        raise ValueError(f"Unknown task type: {task_type}")

    def population_fitness_fn(population: List[EM43Genome]) -> List[float]:
        """
        Evaluate entire population using modular encoder/decoder.
        
        Args:
            population: List of EM43Genome instances
            
        Returns:
            List of fitness scores for each genome
        """
        if not population:
            return []

        # Extract rules and programmes from population
        pop_size = len(population)
        prog_length = len(population[0].programme)

        rules_array = np.zeros((pop_size, 64), dtype=np.uint8)
        programmes_array = np.zeros((pop_size, prog_length), dtype=np.uint8)

        for i, genome in enumerate(population):
            # Apply sanitization
            rules_array[i] = sanitize_rule(genome.rule.get_rule_array())
            programmes_array[i] = sanitize_programme(genome.programme)

        # Use modular population-parallel evaluation
        fitness_scores = evaluate_population_fitness_modular(
            rules_array, programmes_array, inputs, targets,
            simulator.window, simulator.max_steps, simulator.halt_thresh,
            sparsity_penalty, use_continuous_fitness
        )

        return fitness_scores.tolist()

    return population_fitness_fn


def create_modular_doubling_fitness_function(
    simulator: NumbaSimulator,
    input_range: Tuple[int, int] = (1, 30),
    use_population_parallel: bool = True,
    **kwargs
) -> Callable:
    """
    Create a modular fitness function specifically for the doubling task.
    
    Args:
        simulator: NumbaSimulator instance
        input_range: Tuple of (start, end) input values
        use_population_parallel: If True, create population-parallel function
        **kwargs: Additional arguments passed to fitness function factory
        
    Returns:
        Appropriate modular fitness function for the doubling task
    """
    if use_population_parallel and check_numba_availability():
        return create_modular_population_parallel_fitness_function(
            simulator, input_range, task_type="doubling", **kwargs
        )
    else:
        # Fall back to individual evaluation (could implement modular individual version)
        from .fitness import create_individual_fitness_function
        return create_individual_fitness_function(
            simulator, input_range, task_type="doubling", **kwargs
        )
