"""
Binary encoder for cellular automata.

This encoder converts numbers to binary representation where 1's are replaced
with input states, as preferred by the user: 9 -> 01001 -> 02002
"""

import numpy as np
from typing import Tuple, Optional

try:
    import numba as nb
    NUMBA_AVAILABLE = True
except ImportError:
    NUMBA_AVAILABLE = False
    nb = None


class BinaryEncoder:
    """
    Binary encoder for CA inputs.
    
    Converts numbers to binary, then replaces 1's with input state (default: 2 for red).
    Example: 9 -> binary 1001 -> 2002 (with input_state=2)
    
    Format: [programme][BB][binary_representation][0...]
    """
    
    def __init__(self, input_state: int = 2, max_bits: int = 8):
        """
        Initialize binary encoder.
        
        Args:
            input_state: State value to use for binary 1's (default: 2 for red)
            max_bits: Maximum number of bits to use for binary representation
        """
        self.input_state = input_state
        self.max_bits = max_bits
    
    def encode(self, programme: np.ndarray, input_val: int, window: int) -> np.ndarray:
        """
        Encode programme and input into initial CA state using binary encoding.
        
        Args:
            programme: Programme array
            input_val: Input value to encode
            window: Size of CA window
            
        Returns:
            Initial CA state array
        """
        state = np.zeros(window, dtype=np.uint8)
        L = len(programme)
        
        # Write programme
        state[:L] = programme
        
        # Write separator (BB)
        if L < window:
            state[L] = 3  # Blue
        if L + 1 < window:
            state[L + 1] = 3  # Blue
        
        # Convert input to binary and encode
        binary_str = format(input_val, f'0{self.max_bits}b')
        start_pos = L + 2
        
        for i, bit in enumerate(binary_str):
            pos = start_pos + i
            if pos < window:
                if bit == '1':
                    state[pos] = self.input_state  # Red for 1's
                # 0's remain as 0 (empty)
        
        return state
    
    def decode(self, final_state: np.ndarray, programme_length: int) -> int:
        """
        Decode final CA state to output value using binary decoding.
        
        Args:
            final_state: Final CA state after simulation
            programme_length: Length of the programme
            
        Returns:
            Decoded output value
        """
        # Find the binary pattern in the final state
        start_pos = programme_length + 2
        
        # Look for pattern of input_state cells
        binary_str = ""
        for i in range(self.max_bits):
            pos = start_pos + i
            if pos < len(final_state):
                if final_state[pos] == self.input_state:
                    binary_str += "1"
                else:
                    binary_str += "0"
            else:
                binary_str += "0"
        
        # Convert binary string to integer
        try:
            return int(binary_str, 2)
        except ValueError:
            return -10  # Error value


if NUMBA_AVAILABLE:
    @nb.njit(cache=True)
    def _encode_binary_numba(programme: np.ndarray, input_val: int, window: int, 
                            input_state: int, max_bits: int) -> np.ndarray:
        """Numba-compiled binary encoding function."""
        state = np.zeros(window, dtype=np.uint8)
        L = len(programme)
        
        # Write programme
        for i in range(L):
            state[i] = programme[i]
        
        # Write separator (BB)
        if L < window:
            state[L] = 3  # Blue
        if L + 1 < window:
            state[L + 1] = 3  # Blue
        
        # Convert input to binary and encode
        start_pos = L + 2
        
        # Manual binary conversion for Numba compatibility
        temp_val = input_val
        binary_bits = np.zeros(max_bits, dtype=np.uint8)
        
        for i in range(max_bits):
            bit_pos = max_bits - 1 - i
            if temp_val >= (1 << bit_pos):
                binary_bits[i] = 1
                temp_val -= (1 << bit_pos)
        
        # Write binary representation
        for i in range(max_bits):
            pos = start_pos + i
            if pos < window:
                if binary_bits[i] == 1:
                    state[pos] = input_state
        
        return state
    
    @nb.njit(cache=True)
    def _decode_binary_numba(final_state: np.ndarray, programme_length: int,
                            input_state: int, max_bits: int) -> int:
        """Numba-compiled binary decoding function."""
        start_pos = programme_length + 2
        
        # Extract binary pattern
        result = 0
        for i in range(max_bits):
            pos = start_pos + i
            if pos < len(final_state) and final_state[pos] == input_state:
                bit_pos = max_bits - 1 - i
                result += (1 << bit_pos)
        
        return result
    
    @nb.njit(parallel=True, fastmath=True, cache=True)
    def _encode_batch_binary_numba(
        programmes_array: np.ndarray,  # (P, L)
        inputs: np.ndarray,  # (B,)
        window: int,
        input_state: int,
        max_bits: int
    ) -> np.ndarray:  # (P, B, window)
        """Batch binary encoding with Numba optimization."""
        P = programmes_array.shape[0]
        L = programmes_array.shape[1]
        B = inputs.shape[0]
        
        all_states = np.zeros((P, B, window), dtype=np.uint8)
        
        for p in nb.prange(P):
            programme = programmes_array[p]
            
            for b in range(B):
                input_val = inputs[b]
                
                # Write programme
                for j in range(L):
                    all_states[p, b, j] = programme[j]
                
                # Write separator (BB)
                all_states[p, b, L] = 3      # Blue
                all_states[p, b, L + 1] = 3  # Blue
                
                # Binary encoding
                start_pos = L + 2
                temp_val = input_val
                
                for i in range(max_bits):
                    bit_pos = max_bits - 1 - i
                    pos = start_pos + i
                    if pos < window:
                        if temp_val >= (1 << bit_pos):
                            all_states[p, b, pos] = input_state
                            temp_val -= (1 << bit_pos)
        
        return all_states
    
    @nb.njit(parallel=True, fastmath=True, cache=True)
    def _decode_batch_binary_numba(
        final_states: np.ndarray,  # (P, B, window)
        programme_length: int,
        input_state: int,
        max_bits: int
    ) -> np.ndarray:  # (P, B)
        """Batch binary decoding with Numba optimization."""
        P, B, window = final_states.shape
        outputs = np.zeros((P, B), dtype=np.int32)
        
        for p in nb.prange(P):
            for b in range(B):
                start_pos = programme_length + 2
                
                # Extract binary pattern
                result = 0
                for i in range(max_bits):
                    pos = start_pos + i
                    if pos < window and final_states[p, b, pos] == input_state:
                        bit_pos = max_bits - 1 - i
                        result += (1 << bit_pos)
                
                outputs[p, b] = result
        
        return outputs


class BinaryEncoderNumba(BinaryEncoder):
    """Numba-optimized version of BinaryEncoder."""
    
    def __init__(self, input_state: int = 2, max_bits: int = 8):
        """Initialize the Numba-optimized binary encoder."""
        if not NUMBA_AVAILABLE:
            raise RuntimeError("Numba is required for BinaryEncoderNumba")
        super().__init__(input_state, max_bits)
    
    def encode(self, programme: np.ndarray, input_val: int, window: int) -> np.ndarray:
        """Encode using Numba-compiled function."""
        return _encode_binary_numba(programme, input_val, window, 
                                   self.input_state, self.max_bits)
    
    def decode(self, final_state: np.ndarray, programme_length: int) -> int:
        """Decode using Numba-compiled function."""
        return _decode_binary_numba(final_state, programme_length,
                                   self.input_state, self.max_bits)
    
    def encode_batch(self, programmes_array: np.ndarray, inputs: np.ndarray, window: int) -> np.ndarray:
        """Batch encode using Numba-compiled function."""
        return _encode_batch_binary_numba(programmes_array, inputs, window,
                                         self.input_state, self.max_bits)
    
    def decode_batch(self, final_states: np.ndarray, programme_length: int) -> np.ndarray:
        """Batch decode using Numba-compiled function."""
        return _decode_batch_binary_numba(final_states, programme_length,
                                         self.input_state, self.max_bits)


def get_binary_encoder(input_state: int = 2, max_bits: int = 8, use_numba: bool = True) -> BinaryEncoder:
    """
    Get an appropriate binary encoder instance.
    
    Args:
        input_state: State value to use for binary 1's
        max_bits: Maximum number of bits for binary representation
        use_numba: If True and Numba is available, return Numba-optimized encoder
        
    Returns:
        BinaryEncoder instance
    """
    if use_numba and NUMBA_AVAILABLE:
        return BinaryEncoderNumba(input_state, max_bits)
    else:
        return BinaryEncoder(input_state, max_bits)
