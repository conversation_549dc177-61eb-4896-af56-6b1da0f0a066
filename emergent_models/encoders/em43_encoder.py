"""
EM-4/3 specific encoder/decoder for cellular automata.

This module provides modular encoding and decoding for the EM-4/3 system,
separating the encoding logic from the simulation logic.
"""

import numpy as np
from typing import Tuple, Optional

try:
    import numba as nb
    NUMBA_AVAILABLE = True
except ImportError:
    NUMBA_AVAILABLE = False
    nb = None


class EM43Encoder:
    """
    Encoder/decoder for EM-4/3 cellular automata system.
    
    Handles the encoding format: [programme][BB][0^(input+1)][R][0...]
    where BB are blue separators and R is the red input beacon.
    """
    
    def __init__(self):
        """Initialize the EM-4/3 encoder."""
        pass
    
    def encode(self, programme: np.ndarray, input_val: int, window: int) -> np.ndarray:
        """
        Encode programme and input into initial CA state.
        
        Args:
            programme: Programme array
            input_val: Input value to encode
            window: Size of CA window
            
        Returns:
            Initial CA state array
        """
        state = np.zeros(window, dtype=np.uint8)
        L = len(programme)
        
        # Write programme
        state[:L] = programme
        
        # Write separator (BB)
        if L < window:
            state[L] = 3  # Blue
        if L + 1 < window:
            state[L + 1] = 3  # Blue
        
        # Write input as positional encoding
        beacon_pos = L + 2 + input_val + 1
        if beacon_pos < window:
            state[beacon_pos] = 2  # Red beacon
            
        return state
    
    def decode(self, final_state: np.ndarray, programme_length: int) -> int:
        """
        Decode final CA state to output value.

        Args:
            final_state: Final CA state after simulation
            programme_length: Length of the programme

        Returns:
            Decoded output value
        """
        # Find rightmost red cell
        for x in range(len(final_state) - 1, -1, -1):
            if final_state[x] == 2:
                return x - (programme_length + 3)

        # No red cell found - return error value
        return -10


if NUMBA_AVAILABLE:
    @nb.njit(cache=True)
    def _encode_numba(programme: np.ndarray, input_val: int, window: int) -> np.ndarray:
        """Numba-compiled encoding function."""
        state = np.zeros(window, dtype=np.uint8)
        L = len(programme)
        
        # Write programme
        for i in range(L):
            state[i] = programme[i]
        
        # Write separator (BB)
        if L < window:
            state[L] = 3  # Blue
        if L + 1 < window:
            state[L + 1] = 3  # Blue
        
        # Write input as positional encoding
        beacon_pos = L + 2 + input_val + 1
        if beacon_pos < window:
            state[beacon_pos] = 2  # Red beacon
            
        return state
    
    @nb.njit(cache=True)
    def _decode_numba(final_state: np.ndarray, programme_length: int) -> int:
        """Numba-compiled decoding function."""
        # Find rightmost red cell
        for x in range(len(final_state) - 1, -1, -1):
            if final_state[x] == 2:
                return x - (programme_length + 3)

        # No red cell found - return error value
        return -10
    
    @nb.njit(parallel=True, fastmath=True, cache=True)
    def _encode_batch_numba(
        programmes_array: np.ndarray,  # (P, L)
        inputs: np.ndarray,  # (B,)
        window: int
    ) -> np.ndarray:  # (P, B, window)
        """Batch encoding with Numba optimization."""
        P = programmes_array.shape[0]
        L = programmes_array.shape[1]
        B = inputs.shape[0]
        
        all_states = np.zeros((P, B, window), dtype=np.uint8)
        
        for p in nb.prange(P):
            programme = programmes_array[p]
            
            for b in range(B):
                input_val = inputs[b]
                
                # Write programme
                for j in range(L):
                    all_states[p, b, j] = programme[j]
                
                # Write separator (BB)
                all_states[p, b, L] = 3      # Blue
                all_states[p, b, L + 1] = 3  # Blue
                
                # Write input as positional encoding
                beacon_pos = L + 2 + input_val + 1
                if beacon_pos < window:
                    all_states[p, b, beacon_pos] = 2  # Red beacon
        
        return all_states
    
    @nb.njit(parallel=True, fastmath=True, cache=True)
    def _decode_batch_numba(
        final_states: np.ndarray,  # (P, B, window)
        programme_length: int
    ) -> np.ndarray:  # (P, B)
        """Batch decoding with Numba optimization."""
        P, B, window = final_states.shape
        outputs = np.zeros((P, B), dtype=np.int32)
        
        for p in nb.prange(P):
            for b in range(B):
                # Find rightmost red cell
                for x in range(window - 1, -1, -1):
                    if final_states[p, b, x] == 2:
                        outputs[p, b] = x - (programme_length + 3)
                        break
                else:
                    outputs[p, b] = -10  # No red cell found
        
        return outputs


class EM43EncoderNumba(EM43Encoder):
    """Numba-optimized version of EM43Encoder."""
    
    def __init__(self):
        """Initialize the Numba-optimized EM-4/3 encoder."""
        if not NUMBA_AVAILABLE:
            raise RuntimeError("Numba is required for EM43EncoderNumba")
        super().__init__()
    
    def encode(self, programme: np.ndarray, input_val: int, window: int) -> np.ndarray:
        """Encode using Numba-compiled function."""
        return _encode_numba(programme, input_val, window)
    
    def decode(self, final_state: np.ndarray, programme_length: int) -> int:
        """Decode using Numba-compiled function."""
        return _decode_numba(final_state, programme_length)
    
    def encode_batch(self, programmes_array: np.ndarray, inputs: np.ndarray, window: int) -> np.ndarray:
        """Batch encode using Numba-compiled function."""
        return _encode_batch_numba(programmes_array, inputs, window)
    
    def decode_batch(self, final_states: np.ndarray, programme_length: int) -> np.ndarray:
        """Batch decode using Numba-compiled function."""
        return _decode_batch_numba(final_states, programme_length)


def get_encoder(use_numba: bool = True) -> EM43Encoder:
    """
    Get an appropriate encoder instance.
    
    Args:
        use_numba: If True and Numba is available, return Numba-optimized encoder
        
    Returns:
        EM43Encoder instance
    """
    if use_numba and NUMBA_AVAILABLE:
        return EM43EncoderNumba()
    else:
        return EM43Encoder()
