#!/usr/bin/env python3
"""
EM-4/3 Doubling Example - Modular Architecture Edition
======================================================
Train a cellular automaton to perform the doubling operation (x -> 2x)
using the EM-4/3 system with genetic algorithm optimization.

This example demonstrates:
- Creating EM-4/3 genomes with rules and programmes
- Modular encoder/decoder architecture for better separation of concerns
- Setting up batch simulation with Numba acceleration
- Training with genetic algorithm
- Population-parallel fitness evaluation for maximum speed
- Continuous error-based fitness for smoother optimization

Usage:
    # Basic usage with optimized defaults
    python examples/em43_doubling.py

    # Maximum speed with population-parallel evaluation
    python examples/em43_doubling.py --use-population-parallel
    python examples/em43_doubling.py --use-population-parallel --use-continuous-fitness

    # Match fast code parameters exactly
    python examples/em43_doubling.py --population 20000 --generations 300 --max-steps 800

    # Custom experiments
    python examples/em43_doubling.py --population 10000 --generations 200 --use-continuous-fitness
    python examples/em43_doubling.py --input-range-start 1 --input-range-end 50 --window-size 300
"""

import argparse
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import time

# Import emergent_models components
from emergent_models.simulators.numba_simulator import NumbaSimulator
from emergent_models.optimizers.genetic import GAOptimizer
from emergent_models.losses.distance import HammingLoss
from emergent_models.training.trainer import CATrainer, create_accuracy_validator
from emergent_models.utils.visualization import plot_fitness_curve
from emergent_models.training.checkpointing import save_genome, load_genome

# Import new modularized components
from emergent_models.utils.validation import validate_input_range
from emergent_models.training.modular_fitness import create_modular_doubling_fitness_function
from emergent_models.training.population_trainer import PopulationTrainer
from emergent_models.simulators.population_parallel import check_numba_availability

# Numba availability check now handled by SDK
NUMBA_AVAILABLE = check_numba_availability()
if not NUMBA_AVAILABLE:
    print("Warning: Numba not available, population-parallel evaluation disabled")


# ============================================================================
# MODULAR ENCODER/DECODER ARCHITECTURE
# ============================================================================
# This example now uses a modular encoder/decoder architecture that separates
# encoding/decoding logic from the CA simulation for better design:
#
# 1. ENCODING: Input + programme → Initial CA state
#    - Handled by EM43Encoder from emergent_models.encoders.em43_encoder
#    - Format: [programme][BB][0^(input+1)][R][0...]
#    - Encoding logic is separate and configurable
#
# 2. SIMULATION: Initial state → Final state
#    - Handled by modular population-parallel simulator
#    - CA rules applied agnostically to encoded states
#    - No knowledge of encoding format
#
# 3. DECODING: Final CA state → Output value
#    - Handled by EM43Encoder.decode()
#    - Finds rightmost red cell and calculates position
#    - Decoding logic is separate and testable
#
# Benefits:
# - Better separation of concerns
# - Configurable encoders (can easily add binary encoding, etc.)
# - Easier unit testing of individual components
# - More maintainable and extensible code
# - Only ~2% performance overhead for significant architectural benefits
# ============================================================================



def main():
    """Main training loop"""
    parser = argparse.ArgumentParser(description='Train EM-4/3 doubling with optimized parameters')

    # Core parameters (matching fast code defaults)
    parser.add_argument('--population', type=int, default=20000,
                       help='Population size (default: 20000 - matches fast code)')
    parser.add_argument('--generations', type=int, default=300,
                       help='Number of generations (default: 300 - matches fast code)')

    # CA parameters (matching fast code)
    parser.add_argument('--programme-length', type=int, default=10,
                       help='Programme length (default: 10 - matches fast code)')
    parser.add_argument('--window-size', type=int, default=200,
                       help='Window size (default: 200 - matches fast code)')
    parser.add_argument('--max-steps', type=int, default=800,
                       help='Maximum simulation steps (default: 800 - matches fast code)')
    parser.add_argument('--halt-thresh', type=float, default=0.50,
                       help='Halt threshold for simulation (default: 0.50)')

    # Genetic algorithm parameters (matching fast code)
    parser.add_argument('--mutation-rate', type=float, default=0.03,
                       help='Mutation rate (default: 0.03 - matches fast code)')
    parser.add_argument('--programme-mutation-rate', type=float, default=0.08,
                       help='Programme mutation rate (default: 0.08 - matches fast code)')
    parser.add_argument('--elite-fraction', type=float, default=0.10,
                       help='Elite fraction (default: 0.10 - matches fast code)')
    parser.add_argument('--tournament-size', type=int, default=3,
                       help='Tournament size (default: 3)')
    parser.add_argument('--random-immigrant-rate', type=float, default=0.20,
                       help='Random immigrant rate (default: 0.20 - matches fast code)')

    # Input range parameters
    parser.add_argument('--input-range-start', type=int, default=1,
                       help='Input range start (default: 1)')
    parser.add_argument('--input-range-end', type=int, default=30,
                       help='Input range end (default: 30 - matches fast code)')

    # Early stopping parameters
    parser.add_argument('--early-stopping-threshold', type=float, default=1.0,
                       help='Early stopping accuracy threshold (default: 1.0)')
    parser.add_argument('--disable-early-stopping', action='store_true',
                       help='Disable early stopping')

    # Training parameters
    parser.add_argument('--checkpoint-every', type=int, default=50,
                       help='Checkpoint frequency in generations (default: 50)')

    # Fitness evaluation parameters
    parser.add_argument('--disable-population-parallel', action='store_true',
                       help='Disable population-parallel fitness evaluation (NOT recommended - much slower)')
    parser.add_argument('--use-continuous-fitness', action='store_true',
                       help='Use continuous error-based fitness instead of binary accuracy (matches fast code)')
    parser.add_argument('--sparsity-penalty', type=float, default=0.01,
                       help='Sparsity penalty coefficient (default: 0.01 - matches fast code)')

    args = parser.parse_args()

    print("EM-4/3 Doubling Task Training (Optimized Edition)")
    print("=" * 60)

    # Configuration from CLI arguments
    config = {
        'population_size': args.population,
        'generations': args.generations,
        'programme_length': args.programme_length,
        'window_size': args.window_size,
        'max_steps': args.max_steps,
        'halt_thresh': args.halt_thresh,
        'mutation_rate': args.mutation_rate,
        'programme_mutation_rate': args.programme_mutation_rate,
        'elite_fraction': args.elite_fraction,
        'tournament_size': args.tournament_size,
        'random_immigrant_rate': args.random_immigrant_rate,
        'input_range': (args.input_range_start, args.input_range_end),
        'early_stopping_threshold': args.early_stopping_threshold,
        'disable_early_stopping': args.disable_early_stopping,
        'checkpoint_every': args.checkpoint_every,
        'use_population_parallel': not args.disable_population_parallel,  # Default to True
        'use_continuous_fitness': args.use_continuous_fitness,
        'sparsity_penalty': args.sparsity_penalty
    }

    print(f"Configuration:")
    print(f"- Population size: {config['population_size']}")
    print(f"- Generations: {config['generations']}")
    print(f"- Programme length: {config['programme_length']}")
    print(f"- Window size: {config['window_size']}")
    print(f"- Max steps: {config['max_steps']}")
    print(f"- Halt threshold: {config['halt_thresh']}")
    print(f"- Input range: {config['input_range']}")
    print(f"- Mutation rate: {config['mutation_rate']}")
    print(f"- Programme mutation rate: {config['programme_mutation_rate']}")
    print(f"- Elite fraction: {config['elite_fraction']}")
    print(f"- Tournament size: {config['tournament_size']}")
    print(f"- Random immigrant rate: {config['random_immigrant_rate']}")
    if not config['disable_early_stopping']:
        print(f"- Early stopping threshold: {config['early_stopping_threshold']}")
    print(f"- Checkpoint every: {config['checkpoint_every']} generations")
    print(f"- Population-parallel evaluation: {config['use_population_parallel']}")
    print(f"- Continuous fitness: {config['use_continuous_fitness']}")
    print(f"- Sparsity penalty: {config['sparsity_penalty']}")
    print()

    # Validate configuration
    try:
        validate_input_range(
            config['input_range'],
            config['window_size'],
            config['programme_length']
        )
    except ValueError as e:
        print(f"Configuration error: {e}")
        return None, None

    # Create simulator
    simulator = NumbaSimulator(
        window=config['window_size'],
        max_steps=config['max_steps'],
        halt_thresh=config['halt_thresh']
    )

    # Create optimizer
    optimizer = GAOptimizer(
        population_size=config['population_size'],
        mutation_rate=config['mutation_rate'],
        programme_mutation_rate=config['programme_mutation_rate'],
        elite_fraction=config['elite_fraction'],
        tournament_size=config['tournament_size'],
        random_immigrant_rate=config['random_immigrant_rate']
    )

    # Initialize population
    print("Initializing population...")
    optimizer.initialize_population(programme_length=config['programme_length'])

    # Create fitness function based on configuration
    if config['use_population_parallel'] and check_numba_availability():
        print("Using modular population-parallel fitness evaluation for maximum speed!")
        print("✨ Using separate encoder/decoder modules for better architecture")
        if config['use_continuous_fitness']:
            print("Using continuous error-based fitness for smoother optimization (matches fast code)")
        else:
            print("Using binary accuracy-based fitness")

        population_fitness_fn = create_modular_doubling_fitness_function(
            simulator, config['input_range'],
            use_population_parallel=True,
            use_continuous_fitness=config['use_continuous_fitness'],
            sparsity_penalty=config['sparsity_penalty']
        )

        # Wrapper to make it compatible with trainer
        def fitness_fn(genome, sim):
            # This shouldn't be called when using population-parallel
            individual_fn = create_modular_doubling_fitness_function(
                sim, config['input_range'], use_population_parallel=False
            )
            return individual_fn(genome, sim)
    else:
        if config['use_population_parallel']:
            print("Warning: Population-parallel evaluation requested but Numba not available")
        print("Using standard individual fitness evaluation with modular encoding")
        fitness_fn = create_modular_doubling_fitness_function(
            simulator, config['input_range'], use_population_parallel=False
        )
        population_fitness_fn = None

    # Create validation function for early stopping
    test_inputs = list(range(config['input_range'][0], min(config['input_range'][0] + 10, config['input_range'][1] + 1)))
    test_targets = [2 * x for x in test_inputs]  # Expected outputs
    validation_fn = create_accuracy_validator(test_inputs, test_targets)

    # Training with configurable early stopping
    print("Starting training...")
    if not config['disable_early_stopping']:
        print(f"Early stopping enabled: will stop if {config['early_stopping_threshold']:.1%} accuracy is reached")
    else:
        print("Early stopping disabled")
    start_time = time.time()

    # Training with unified interface
    if config['use_population_parallel'] and check_numba_availability():
        print("Using custom training loop with population-parallel evaluation")

        # Create and run population trainer
        pop_trainer = PopulationTrainer(optimizer)
        results = pop_trainer.train(
            population_fitness_fn=population_fitness_fn,
            validation_fn=validation_fn,
            generations=config['generations'],
            early_stopping_threshold=config['early_stopping_threshold'],
            disable_early_stopping=config['disable_early_stopping'],
            checkpoint_every=config['checkpoint_every'],
            verbose=True
        )

        # Extract results directly
        best_genome = results['best_genome']
        best_fitness = results['best_fitness']
        history = results['history']
        training_stats = pop_trainer.get_training_stats()

    else:
        # Use standard trainer
        trainer = CATrainer(simulator, optimizer, HammingLoss(), verbose=True)

        if config['disable_early_stopping']:
            trainer.fit(
                fitness_fn=fitness_fn,
                epochs=config['generations'],
                checkpoint_every=config['checkpoint_every']
            )
        else:
            trainer.fit(
                fitness_fn=fitness_fn,
                epochs=config['generations'],
                checkpoint_every=config['checkpoint_every'],
                early_stopping_threshold=config['early_stopping_threshold'],
                early_stopping_metric="accuracy",
                validation_fn=validation_fn
            )

        # Extract results from standard trainer
        best_genome = trainer.best_genome
        best_fitness = trainer.best_fitness
        history = trainer.history if hasattr(trainer, 'history') else None
        training_stats = trainer.get_training_stats() if hasattr(trainer, 'get_training_stats') else None

    training_time = time.time() - start_time
    print(f"\nTraining completed in {training_time:.2f} seconds")

    # Results (now using extracted variables instead of trainer attributes)
    print(f"\nBest fitness: {best_fitness:.4f}")
    print(f"Best genome programme: {best_genome.programme}")

    # Test the best genome
    print("\nTesting best genome:")
    # Test on a subset of the input range for display
    test_start = config['input_range'][0]
    test_end = min(config['input_range'][0] + 15, config['input_range'][1] + 1)
    test_inputs = list(range(test_start, test_end))
    test_outputs = simulator.simulate_batch(best_genome, test_inputs)

    print("Input -> Expected -> Actual -> Status")
    print("-" * 40)
    for inp, out in zip(test_inputs, test_outputs):
        expected = 2 * inp
        status = "✓" if out == expected else "✗"
        print(f"{inp:2d} -> {expected:2d} -> {out:2d} {status}")

    # Calculate final accuracy on full input range
    full_inputs = list(range(config['input_range'][0], config['input_range'][1] + 1))
    full_outputs = simulator.simulate_batch(best_genome, full_inputs)
    correct = sum(1 for inp, out in zip(full_inputs, full_outputs) if out == 2 * inp)
    accuracy = correct / len(full_inputs)
    print(f"\nAccuracy on full input range {config['input_range']}: {accuracy:.2%} ({correct}/{len(full_inputs)})")

    # Save results
    results_dir = Path("results")
    results_dir.mkdir(exist_ok=True)

    # Save best genome
    save_genome(best_genome, results_dir / "best_doubling_genome.json")
    print(f"Saved best genome to {results_dir / 'best_doubling_genome.json'}")

    # Plot fitness curve
    if history:
        plot_fitness_curve(history, save_path=results_dir / "fitness_curve.png")
        print(f"Saved fitness curve to {results_dir / 'fitness_curve.png'}")

    # Print training statistics
    if training_stats:
        print(f"\nTraining Statistics:")
        print(f"- Total generations: {training_stats['generations']}")
        print(f"- Best fitness achieved: {training_stats['best_fitness']:.4f}")
        print(f"- Final mean fitness: {training_stats['final_mean_fitness']:.4f}")
        print(f"- Average time per generation: {training_stats['avg_generation_time']:.2f}s")
    else:
        # Fallback for cases where training_stats is not available
        print(f"\nTraining Statistics:")
        print(f"- Total generations: {config['generations']}")
        print(f"- Best fitness achieved: {best_fitness:.4f}")
        if history:
            print(f"- Final mean fitness: {history['mean_fitness'][-1]:.4f}")
            print(f"- Average time per generation: {np.mean(history['generation_times']):.2f}s")

    return best_genome, history


def test_saved_genome():
    """Test a previously saved genome"""
    try:
        genome = load_genome("results/best_doubling_genome.json")
        print(f"Loaded genome with fitness: {genome.fitness:.4f}")
        
        simulator = NumbaSimulator(window=200, max_steps=256, halt_thresh=0.50)
        
        # Test on doubling task
        test_inputs = list(range(1, 21))
        outputs = simulator.simulate_batch(genome, test_inputs)
        
        print("\nTest Results:")
        print("Input -> Expected -> Actual")
        print("-" * 30)
        
        correct = 0
        for inp, out in zip(test_inputs, outputs):
            expected = 2 * inp
            status = "✓" if out == expected else "✗"
            if out == expected:
                correct += 1
            print(f"{inp:2d} -> {expected:2d} -> {out:2d} {status}")
        
        accuracy = correct / len(test_inputs)
        print(f"\nAccuracy: {accuracy:.2%}")
        
    except FileNotFoundError:
        print("No saved genome found. Run training first.")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        test_saved_genome()
    else:
        main()
