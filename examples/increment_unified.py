#!/usr/bin/env python3
"""
EM-4/3 Unified Increment Example
================================
Train a cellular automaton to perform the increment operation (x -> x+1)
with configurable encoding (binary or positional).

This demonstrates the proper modular architecture:
1. Encoder encodes inputs + programme → initial space state
2. Simulator simulates initial state + rules → final space state  
3. Encoder decodes final state → output
4. Fitness calculated based on decoded output

Usage:
    # Basic usage with defaults (now uses larger population for better convergence)
    python examples/increment_unified.py --encoding binary
    python examples/increment_unified.py --encoding positional

    # Maximum speed with population-parallel evaluation
    python examples/increment_unified.py --encoding binary --use-population-parallel
    python examples/increment_unified.py --encoding binary --use-population-parallel --use-continuous-fitness

    # Custom parameters
    python examples/increment_unified.py --encoding binary --generations 200 --population 10000
    python examples/increment_unified.py --encoding positional --mutation-rate 0.05 --elite-fraction 0.15

    # Disable early stopping for full training
    python examples/increment_unified.py --encoding binary --disable-early-stopping

    # Custom input range and CA parameters for harder problems
    python examples/increment_unified.py --encoding binary --input-range-start 1 --input-range-end 30 --max-steps 600
"""

import argparse
import numpy as np
import time
from pathlib import Path

try:
    import numba as nb
    NUMBA_AVAILABLE = True
except ImportError:
    NUMBA_AVAILABLE = False
    print("Warning: Numba not available, population-parallel evaluation disabled")

# Import emergent_models components
from emergent_models.rules.em43 import EM43Rule, EM43Genome
from emergent_models.simulators.numba_simulator import NumbaSimulator
from emergent_models.optimizers.genetic import GAOptimizer
from emergent_models.losses.distance import HammingLoss
from emergent_models.training.trainer import CATrainer
from emergent_models.utils.visualization import plot_fitness_curve
from emergent_models.training.checkpointing import save_genome, load_genome
from emergent_models.encoders.binary import EM43BinaryEncoder
from emergent_models.encoders.position import EM43PositionalEncoder


# Population-parallel fitness evaluation functions
if NUMBA_AVAILABLE:
    @nb.njit(parallel=True, fastmath=True, cache=True)
    def _evaluate_population_parallel_binary(
        rules_array: np.ndarray,
        programmes_array: np.ndarray,
        inputs: np.ndarray,
        targets: np.ndarray,
        window: int,
        max_steps: int,
        halt_thresh: float,
        sparsity_penalty: float,
        use_continuous_fitness: bool
    ) -> np.ndarray:
        """
        Evaluate entire population in parallel for binary encoding.

        Parameters
        ----------
        rules_array : (P, 64) uint8 - Rule lookup tables for each genome
        programmes_array : (P, L) uint8 - Programmes for each genome
        inputs : (N,) int64 - Input values
        targets : (N,) int64 - Target output values
        window : int - Tape length
        max_steps : int - Maximum simulation steps
        halt_thresh : float - Halting threshold
        sparsity_penalty : float - Sparsity penalty coefficient
        use_continuous_fitness : bool - Use continuous error vs binary accuracy

        Returns
        -------
        fitness : (P,) float32 - Fitness scores for each genome
        """
        P = rules_array.shape[0]  # Population size
        L = programmes_array.shape[1]  # Programme length
        N_inputs = inputs.shape[0]

        fitness = np.empty(P, dtype=np.float32)

        # Evaluate each genome in parallel
        for p in nb.prange(P):
            rule = rules_array[p]
            programme = programmes_array[p]

            # Simulate all inputs for this genome
            outputs = np.full(N_inputs, -10, dtype=np.int32)

            for i in range(N_inputs):
                input_val = inputs[i]

                # Create initial state with binary encoding
                state = np.zeros(window, dtype=np.uint8)

                # Write programme
                for j in range(L):
                    state[j] = programme[j]

                # Write separator (BB)
                state[L] = 3      # Blue
                state[L + 1] = 3  # Blue

                # Use positional encoding like the fast code (simpler and more reliable)
                # Write beacon pattern: 0^(input+1) R 0
                beacon_pos = L + 2 + input_val + 1
                if beacon_pos < window:
                    state[beacon_pos] = 2  # Red beacon

                # Simulate
                halted = False
                for step in range(max_steps):
                    if halted:
                        break

                    next_state = np.zeros(window, dtype=np.uint8)

                    # Apply CA rules (boundary cells stay 0)
                    for x in range(1, window - 1):
                        left = state[x - 1]
                        center = state[x]
                        right = state[x + 1]
                        idx = (left << 4) | (center << 2) | right
                        next_state[x] = rule[idx]

                    state = next_state

                    # Check halting condition
                    live_count = 0
                    blue_count = 0
                    for x in range(window):
                        if state[x] != 0:
                            live_count += 1
                            if state[x] == 3:  # Blue
                                blue_count += 1

                    if live_count > 0 and blue_count / live_count >= halt_thresh:
                        halted = True

                # Decode output (find rightmost red beacon like the fast code)
                if halted:
                    # Find rightmost red beacon
                    rightmost_red = -1
                    for x in range(window - 1, -1, -1):
                        if state[x] == 2:  # Red
                            rightmost_red = x
                            break

                    if rightmost_red != -1:
                        # Calculate output relative to expected beacon position
                        expected_beacon = L + 2 + input_val + 1
                        outputs[i] = rightmost_red - expected_beacon

            # Calculate fitness for this genome
            if use_continuous_fitness:
                # Continuous fitness: mean absolute error
                total_error = 0.0
                valid_outputs = 0
                for i in range(N_inputs):
                    if outputs[i] >= 0:
                        total_error += abs(outputs[i] - targets[i])
                        valid_outputs += 1

                if valid_outputs > 0:
                    avg_error = total_error / valid_outputs
                    accuracy_score = -avg_error  # Negative error (higher is better)
                else:
                    accuracy_score = -1000.0  # Large penalty for no valid outputs
            else:
                # Binary accuracy fitness
                correct = 0
                for i in range(N_inputs):
                    if outputs[i] >= 0 and outputs[i] == targets[i]:
                        correct += 1
                accuracy_score = correct / N_inputs

            # Add sparsity penalty
            sparsity = 0
            for j in range(L):
                if programme[j] != 0:
                    sparsity += 1
            sparsity_pen = sparsity_penalty * sparsity / L

            fitness[p] = accuracy_score - sparsity_pen

        return fitness


def create_population_parallel_fitness_function(
    simulator: NumbaSimulator,
    encoder,
    input_range=(1, 15),
    use_continuous_fitness=False,
    sparsity_penalty=0.01
):
    """Create a population-parallel fitness function for maximum speed"""
    if not NUMBA_AVAILABLE:
        print("Warning: Numba not available, falling back to standard fitness function")
        return create_unified_fitness_function(simulator, encoder, input_range)

    inputs = np.array(list(range(input_range[0], input_range[1] + 1)), dtype=np.int64)
    targets = np.array([x + 1 for x in inputs], dtype=np.int64)

    def population_fitness_fn(population):
        """Evaluate entire population at once"""
        if not population:
            return []

        # Extract rules and programmes from population
        pop_size = len(population)
        prog_length = len(population[0].programme)

        rules_array = np.zeros((pop_size, 64), dtype=np.uint8)
        programmes_array = np.zeros((pop_size, prog_length), dtype=np.uint8)

        for i, genome in enumerate(population):
            rules_array[i] = genome.rule.get_rule_array()
            programmes_array[i] = genome.programme

        # Use population-parallel evaluation
        if isinstance(encoder, EM43BinaryEncoder):
            fitness_scores = _evaluate_population_parallel_binary(
                rules_array, programmes_array, inputs, targets,
                simulator.window, simulator.max_steps, simulator.halt_thresh,
                sparsity_penalty, use_continuous_fitness
            )
        else:
            # For positional encoding, fall back to standard method for now
            fitness_scores = []
            standard_fn = create_unified_fitness_function(simulator, encoder, input_range)
            for genome in population:
                fitness_scores.append(standard_fn(genome, simulator))
            fitness_scores = np.array(fitness_scores, dtype=np.float32)

        return fitness_scores.tolist()

    return population_fitness_fn


def create_unified_fitness_function(simulator: NumbaSimulator, encoder, input_range=(1, 15)):
    """Create a fitness function that works with any encoder"""
    inputs = list(range(input_range[0], input_range[1] + 1))
    targets = [x + 1 for x in inputs]

    def fitness_fn(genome: EM43Genome, sim: NumbaSimulator) -> float:
        """Evaluate genome fitness using batch processing for speed"""
        try:
            # Create all initial spaces at once
            initial_spaces = []
            for input_val in inputs:
                initial_space = encoder.encode_input(
                    genome.programme, input_val, sim.window
                )
                initial_spaces.append(initial_space)

            # Batch simulate all spaces at once
            final_spaces = sim.simulate_spaces(genome, initial_spaces)

            # Decode all outputs
            correct = 0
            for i, (input_val, target_val) in enumerate(zip(inputs, targets)):
                final_space = final_spaces[i]

                if isinstance(encoder, EM43BinaryEncoder):
                    output_val = encoder.decode_output(final_space, len(genome.programme))
                else:  # EM43PositionalEncoder
                    output_val = encoder.decode_output(final_space, len(genome.programme), input_val)

                if output_val >= 0 and output_val == target_val:
                    correct += 1

            accuracy = correct / len(inputs)

            # Add sparsity penalty
            sparsity_penalty = 0.01 * np.count_nonzero(genome.programme) / len(genome.programme)

            return accuracy - sparsity_penalty

        except Exception as e:
            print(f"Error evaluating genome: {e}")
            return -1.0

    return fitness_fn


def create_unified_validation_function(encoder, test_inputs, test_targets):
    """Create validation function that works with any encoder"""
    def validate(genome, sim):
        try:
            # Create all initial spaces at once
            initial_spaces = []
            for input_val in test_inputs:
                initial_space = encoder.encode_input(
                    genome.programme, input_val, sim.window
                )
                initial_spaces.append(initial_space)

            # Batch simulate all spaces
            final_spaces = sim.simulate_spaces(genome, initial_spaces)

            # Decode all outputs
            correct = 0
            for i, (input_val, target_val) in enumerate(zip(test_inputs, test_targets)):
                final_space = final_spaces[i]

                if isinstance(encoder, EM43BinaryEncoder):
                    output_val = encoder.decode_output(final_space, len(genome.programme))
                else:  # EM43PositionalEncoder
                    output_val = encoder.decode_output(final_space, len(genome.programme), input_val)

                if output_val >= 0 and output_val == target_val:
                    correct += 1

            return correct / len(test_inputs)

        except Exception as e:
            print(f"Validation error: {e}")
            return 0.0

    return validate


def train_with_population_parallel(optimizer, population_fitness_fn, validation_fn, config):
    """Custom training loop with population-parallel fitness evaluation"""
    from tqdm import tqdm

    history = {
        'best_fitness': [],
        'mean_fitness': [],
        'std_fitness': [],
        'generation_times': []
    }

    best_genome = None
    best_fitness = float('-inf')

    pbar = tqdm(range(config['generations']), desc="Training")

    for generation in pbar:
        gen_start_time = time.time()

        # Evaluate entire population at once
        fitness_scores = population_fitness_fn(optimizer.population)

        # Update best genome
        max_fitness_idx = np.argmax(fitness_scores)
        if fitness_scores[max_fitness_idx] > best_fitness:
            best_fitness = fitness_scores[max_fitness_idx]
            best_genome = optimizer.population[max_fitness_idx]
            if hasattr(best_genome, 'clone'):
                best_genome = best_genome.clone()
            best_genome.fitness = best_fitness

        # Record history
        history['best_fitness'].append(np.max(fitness_scores))
        history['mean_fitness'].append(np.mean(fitness_scores))
        history['std_fitness'].append(np.std(fitness_scores))

        # Evolution step
        optimizer.step(fitness_scores)

        gen_time = time.time() - gen_start_time
        history['generation_times'].append(gen_time)

        # Update progress bar
        pbar.set_postfix({
            'best': f"{best_fitness:.4f}",
            'mean': f"{np.mean(fitness_scores):.4f}",
            'time': f"{gen_time:.2f}s"
        })

        # Early stopping check
        if not config['disable_early_stopping'] and validation_fn:
            # Create a dummy simulator for validation (we need the simulator object)
            from emergent_models.simulators.numba_simulator import NumbaSimulator
            dummy_sim = NumbaSimulator(window=config['window_size'], max_steps=config['max_steps'], halt_thresh=config['halt_thresh'])
            validation_accuracy = validation_fn(best_genome, dummy_sim)
            if validation_accuracy >= config['early_stopping_threshold']:
                print(f"\nEarly stopping at generation {generation + 1}: "
                      f"validation accuracy {validation_accuracy:.1%} >= {config['early_stopping_threshold']:.1%}")
                break

        # Checkpoint saving
        if (generation + 1) % config['checkpoint_every'] == 0:
            print(f"\nGeneration {generation + 1}: best={best_fitness:.4f}, "
                  f"mean={np.mean(fitness_scores):.4f}, time={gen_time:.2f}s")

    return best_genome, history


def main():
    """Main training loop"""
    parser = argparse.ArgumentParser(description='Train EM-4/3 increment with configurable encoding')

    # Core parameters
    parser.add_argument('--encoding', choices=['binary', 'positional'], default='binary',
                       help='Encoding method to use')
    parser.add_argument('--generations', type=int, default=100,
                       help='Number of generations (default: 100)')
    parser.add_argument('--population', type=int, default=5000,
                       help='Population size (default: 5000 - increased for better convergence)')

    # CA parameters (updated defaults based on fast code)
    parser.add_argument('--programme-length', type=int, default=None,
                       help='Programme length (default: 10 for binary, 8 for positional - increased for more complex programs)')
    parser.add_argument('--window-size', type=int, default=None,
                       help='Window size (default: 200 for binary, 250 for positional - increased for larger problems)')
    parser.add_argument('--max-steps', type=int, default=400,
                       help='Maximum simulation steps (default: 400 - increased for more complex evolution)')
    parser.add_argument('--halt-thresh', type=float, default=0.50,
                       help='Halt threshold for simulation (default: 0.50)')

    # Genetic algorithm parameters (updated defaults based on fast code)
    parser.add_argument('--mutation-rate', type=float, default=0.03,
                       help='Mutation rate (default: 0.03 - optimized for faster convergence)')
    parser.add_argument('--programme-mutation-rate', type=float, default=0.08,
                       help='Programme mutation rate (default: 0.08 - optimized for faster convergence)')
    parser.add_argument('--elite-fraction', type=float, default=0.10,
                       help='Elite fraction (default: 0.10 - optimized for faster convergence)')
    parser.add_argument('--tournament-size', type=int, default=3,
                       help='Tournament size (default: 3)')
    parser.add_argument('--random-immigrant-rate', type=float, default=0.20,
                       help='Random immigrant rate (default: 0.20 - optimized for faster convergence)')

    # Input range parameters
    parser.add_argument('--input-range-start', type=int, default=None,
                       help='Input range start (default: 1)')
    parser.add_argument('--input-range-end', type=int, default=None,
                       help='Input range end (default: 15 for binary, 20 for positional)')

    # Early stopping parameters
    parser.add_argument('--early-stopping-threshold', type=float, default=1.0,
                       help='Early stopping accuracy threshold (default: 1.0)')
    parser.add_argument('--disable-early-stopping', action='store_true',
                       help='Disable early stopping')

    # Training parameters
    parser.add_argument('--checkpoint-every', type=int, default=25,
                       help='Checkpoint frequency in generations (default: 25)')

    # Binary encoder parameters
    parser.add_argument('--bit-width', type=int, default=8,
                       help='Bit width for binary encoding (default: 8)')
    parser.add_argument('--input-state', type=int, default=2,
                       help='Input state for binary encoding (default: 2)')

    # Positional encoder parameters
    parser.add_argument('--beacon-state', type=int, default=2,
                       help='Beacon state for positional encoding (default: 2)')
    parser.add_argument('--separator-state', type=int, default=3,
                       help='Separator state for positional encoding (default: 3)')

    # Fitness evaluation parameters
    parser.add_argument('--use-population-parallel', action='store_true',
                       help='Use population-parallel fitness evaluation for maximum speed')
    parser.add_argument('--use-continuous-fitness', action='store_true',
                       help='Use continuous error-based fitness instead of binary accuracy')
    parser.add_argument('--sparsity-penalty', type=float, default=0.01,
                       help='Sparsity penalty coefficient (default: 0.01)')

    args = parser.parse_args()
    
    print(f"EM-4/3 Increment Task Training ({args.encoding.upper()} encoding)")
    print("=" * 60)

    # Set encoding-specific defaults if not provided (updated for better performance)
    if args.programme_length is None:
        args.programme_length = 10 if args.encoding == 'binary' else 8
    if args.window_size is None:
        args.window_size = 200 if args.encoding == 'binary' else 250
    if args.input_range_start is None:
        args.input_range_start = 1
    if args.input_range_end is None:
        args.input_range_end = 20 if args.encoding == 'binary' else 25

    # Create encoder based on choice
    if args.encoding == 'binary':
        encoder = EM43BinaryEncoder(bit_width=args.bit_width, input_state=args.input_state)
        print("Using binary encoding: numbers → binary → state mapping")
        print(f"Example: 5 → 00000101 → [0,0,0,0,0,{args.input_state},0,{args.input_state}]")
        print(f"Bit width: {args.bit_width}, Input state: {args.input_state}")
    else:
        encoder = EM43PositionalEncoder(beacon_state=args.beacon_state, separator_state=args.separator_state)
        print("Using positional encoding: numbers → positional → beacon placement")
        print(f"Example: 5 → [0,0,0,0,0,{args.beacon_state}] (5 zeros + beacon)")
        print(f"Beacon state: {args.beacon_state}, Separator state: {args.separator_state}")

    # Configuration from CLI arguments
    config = {
        'population_size': args.population,
        'generations': args.generations,
        'programme_length': args.programme_length,
        'window_size': args.window_size,
        'max_steps': args.max_steps,
        'halt_thresh': args.halt_thresh,
        'mutation_rate': args.mutation_rate,
        'programme_mutation_rate': args.programme_mutation_rate,
        'elite_fraction': args.elite_fraction,
        'tournament_size': args.tournament_size,
        'random_immigrant_rate': args.random_immigrant_rate,
        'input_range': (args.input_range_start, args.input_range_end),
        'early_stopping_threshold': args.early_stopping_threshold,
        'disable_early_stopping': args.disable_early_stopping,
        'checkpoint_every': args.checkpoint_every,
        'use_population_parallel': args.use_population_parallel,
        'use_continuous_fitness': args.use_continuous_fitness,
        'sparsity_penalty': args.sparsity_penalty
    }
    
    print(f"\nConfiguration:")
    print(f"- Population size: {config['population_size']}")
    print(f"- Generations: {config['generations']}")
    print(f"- Programme length: {config['programme_length']}")
    print(f"- Window size: {config['window_size']}")
    print(f"- Max steps: {config['max_steps']}")
    print(f"- Halt threshold: {config['halt_thresh']}")
    print(f"- Input range: {config['input_range']}")
    print(f"- Mutation rate: {config['mutation_rate']}")
    print(f"- Programme mutation rate: {config['programme_mutation_rate']}")
    print(f"- Elite fraction: {config['elite_fraction']}")
    print(f"- Tournament size: {config['tournament_size']}")
    print(f"- Random immigrant rate: {config['random_immigrant_rate']}")
    if not config['disable_early_stopping']:
        print(f"- Early stopping threshold: {config['early_stopping_threshold']}")
    print(f"- Checkpoint every: {config['checkpoint_every']} generations")
    print(f"- Population-parallel evaluation: {config['use_population_parallel']}")
    print(f"- Continuous fitness: {config['use_continuous_fitness']}")
    print(f"- Sparsity penalty: {config['sparsity_penalty']}")
    print()
    
    # Create simulator
    simulator = NumbaSimulator(
        window=config['window_size'],
        max_steps=config['max_steps'],
        halt_thresh=config['halt_thresh']
    )
    
    # Create optimizer
    optimizer = GAOptimizer(
        population_size=config['population_size'],
        mutation_rate=config['mutation_rate'],
        programme_mutation_rate=config['programme_mutation_rate'],
        elite_fraction=config['elite_fraction'],
        tournament_size=config['tournament_size'],
        random_immigrant_rate=config['random_immigrant_rate']
    )
    
    # Initialize population
    print("Initializing population...")
    optimizer.initialize_population(programme_length=config['programme_length'])

    # Create fitness function based on configuration
    if config['use_population_parallel'] and NUMBA_AVAILABLE:
        print("Using population-parallel fitness evaluation for maximum speed!")
        if config['use_continuous_fitness']:
            print("Using continuous error-based fitness for smoother optimization")
        else:
            print("Using binary accuracy-based fitness")

        population_fitness_fn = create_population_parallel_fitness_function(
            simulator, encoder, config['input_range'],
            use_continuous_fitness=config['use_continuous_fitness'],
            sparsity_penalty=config['sparsity_penalty']
        )

        # Wrapper to make it compatible with trainer
        def fitness_fn(genome, sim):
            # This shouldn't be called when using population-parallel
            return create_unified_fitness_function(simulator, encoder, config['input_range'])(genome, sim)
    else:
        if config['use_population_parallel']:
            print("Warning: Population-parallel evaluation requested but Numba not available")
        print("Using standard individual fitness evaluation")
        fitness_fn = create_unified_fitness_function(
            simulator, encoder, config['input_range']
        )
        population_fitness_fn = None
    
    # Create validation function for early stopping
    test_inputs = list(range(1, 8))
    test_targets = [x + 1 for x in test_inputs]
    validation_fn = create_unified_validation_function(
        encoder, test_inputs, test_targets
    )
    
    # Training with configurable early stopping
    print("Starting training...")
    if not config['disable_early_stopping']:
        print(f"Early stopping enabled: will stop if {config['early_stopping_threshold']:.1%} accuracy is reached")
    else:
        print("Early stopping disabled")
    start_time = time.time()

    # Use custom training loop for population-parallel evaluation
    if config['use_population_parallel'] and NUMBA_AVAILABLE:
        print("Using custom training loop with population-parallel evaluation")
        best_genome, history = train_with_population_parallel(
            optimizer, population_fitness_fn, validation_fn, config
        )

        # Create a mock trainer object for compatibility
        class MockTrainer:
            def __init__(self, best_genome, best_fitness, history):
                self.best_genome = best_genome
                self.best_fitness = best_fitness
                self.history = history

        trainer = MockTrainer(best_genome, best_genome.fitness if hasattr(best_genome, 'fitness') else 0.0, history)
    else:
        # Use standard trainer
        trainer = CATrainer(simulator, optimizer, HammingLoss(), verbose=True)

        if config['disable_early_stopping']:
            trainer.fit(
                fitness_fn=fitness_fn,
                epochs=config['generations'],
                checkpoint_every=config['checkpoint_every']
            )
        else:
            trainer.fit(
                fitness_fn=fitness_fn,
                epochs=config['generations'],
                checkpoint_every=config['checkpoint_every'],
                early_stopping_threshold=config['early_stopping_threshold'],
                early_stopping_metric="accuracy",
                validation_fn=validation_fn
            )
    
    training_time = time.time() - start_time
    print(f"\nTraining completed in {training_time:.2f} seconds")
    
    # Test the best genome
    best_genome = trainer.best_genome
    print(f"\nBest fitness: {trainer.best_fitness:.4f}")
    print(f"Best genome programme: {best_genome.programme}")
    
    # Test on full range
    print(f"\nTesting best genome with {args.encoding} encoding:")
    test_inputs_full = list(range(1, 16))
    
    print("Input -> Expected -> Actual -> Status")
    print("-" * 35)
    
    correct = 0
    for inp in test_inputs_full:
        expected = inp + 1
        
        try:
            # Use modular architecture for testing
            initial_space = encoder.encode_input(best_genome.programme, inp, simulator.window)
            final_spaces = simulator.simulate_spaces(best_genome, [initial_space])
            final_space = final_spaces[0]
            
            if isinstance(encoder, EM43BinaryEncoder):
                out = encoder.decode_output(final_space, len(best_genome.programme))
            else:
                out = encoder.decode_output(final_space, len(best_genome.programme), inp)
            
            status = "✓" if out == expected else "✗"
            if out == expected:
                correct += 1
            print(f"{inp:2d} -> {expected:2d} -> {out:2d} {status}")
            
        except Exception as e:
            print(f"{inp:2d} -> {expected:2d} -> ERR {e}")
    
    accuracy = correct / len(test_inputs_full)
    print(f"\nAccuracy: {accuracy:.2%}")
    
    # Save results
    results_dir = Path("results")
    results_dir.mkdir(exist_ok=True)
    
    filename = f"best_{args.encoding}_increment_genome.json"
    save_genome(best_genome, results_dir / filename)
    print(f"Saved best genome to {results_dir / filename}")
    
    # Plot fitness curve
    plot_filename = f"{args.encoding}_increment_fitness_curve.png"
    plot_fitness_curve(trainer.history, save_path=results_dir / plot_filename)
    print(f"Saved fitness curve to {results_dir / plot_filename}")
    
    return best_genome, trainer.history


if __name__ == "__main__":
    main()
