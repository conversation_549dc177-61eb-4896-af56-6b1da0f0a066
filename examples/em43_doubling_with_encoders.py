#!/usr/bin/env python3
"""
EM-4/3 Doubling Example with Configurable Encoders
==================================================
Demonstrates how to use different encoders (EM-4/3 positional, binary, custom)
in the high-level training code.

This example shows:
- How to use EM-4/3 positional encoding (original)
- How to use binary encoding (9 -> 01001 -> 02002)
- How to create and use custom encoders
- How to compare different encoding strategies
"""

import argparse
import numpy as np
import time
from pathlib import Path

# Import emergent_models components
from emergent_models.simulators.numba_simulator import NumbaSimulator
from emergent_models.optimizers.genetic import GAOptimizer
from emergent_models.training.population_trainer import PopulationTrainer
from emergent_models.utils.validation import validate_input_range
from emergent_models.simulators.population_parallel import check_numba_availability

# Import configurable fitness functions
from emergent_models.training.configurable_fitness import (
    create_doubling_fitness_with_encoder,
    create_em43_fitness_function,
    create_binary_fitness_function,
    create_custom_encoder_fitness_function,
    ExampleCustomEncoder
)

# Import encoders directly for advanced usage
from emergent_models.encoders.em43_encoder import get_encoder as get_em43_encoder
from emergent_models.encoders.binary_encoder import get_binary_encoder


def main():
    """Main function demonstrating different encoders."""
    
    parser = argparse.ArgumentParser(description='Train EM-4/3 doubling with different encoders')
    
    # Encoder selection
    parser.add_argument('--encoder', type=str, default='em43',
                       choices=['em43', 'binary', 'custom'],
                       help='Encoder type to use (default: em43)')
    
    # Binary encoder specific options
    parser.add_argument('--binary-bits', type=int, default=8,
                       help='Number of bits for binary encoding (default: 8)')
    parser.add_argument('--binary-state', type=int, default=2,
                       help='State value for binary 1s (default: 2 for red)')
    
    # Training parameters
    parser.add_argument('--population', type=int, default=500,
                       help='Population size (default: 500)')
    parser.add_argument('--generations', type=int, default=20,
                       help='Number of generations (default: 20)')
    parser.add_argument('--input-range-end', type=int, default=15,
                       help='Input range end (default: 15)')
    parser.add_argument('--window-size', type=int, default=100,
                       help='Window size (default: 100)')
    parser.add_argument('--max-steps', type=int, default=200,
                       help='Maximum simulation steps (default: 200)')

    # Early stopping parameters
    parser.add_argument('--early-stopping-threshold', type=float, default=1.0,
                       help='Early stopping accuracy threshold (default: 1.0 = 100%)')
    parser.add_argument('--disable-early-stopping', action='store_true',
                       help='Disable early stopping')
    parser.add_argument('--checkpoint-every', type=int, default=10,
                       help='Checkpoint frequency in generations (default: 10)')
    
    args = parser.parse_args()
    
    print(f"EM-4/3 Doubling with {args.encoder.upper()} Encoding")
    print("=" * 60)
    
    # Configuration
    config = {
        'population_size': args.population,
        'generations': args.generations,
        'programme_length': 10,
        'window_size': args.window_size,
        'max_steps': args.max_steps,
        'halt_thresh': 0.50,
        'input_range': (1, args.input_range_end),
        'sparsity_penalty': 0.01,
        'use_continuous_fitness': False,
        'early_stopping_threshold': args.early_stopping_threshold,
        'disable_early_stopping': args.disable_early_stopping,
        'checkpoint_every': args.checkpoint_every
    }
    
    print(f"Configuration:")
    print(f"- Encoder: {args.encoder}")
    if args.encoder == 'binary':
        print(f"- Binary bits: {args.binary_bits}")
        print(f"- Binary state: {args.binary_state}")
    print(f"- Population: {config['population_size']}")
    print(f"- Generations: {config['generations']}")
    print(f"- Input range: {config['input_range']}")
    print(f"- Window size: {config['window_size']}")
    if not config['disable_early_stopping']:
        print(f"- Early stopping threshold: {config['early_stopping_threshold']:.1%}")
    else:
        print(f"- Early stopping: disabled")
    print(f"- Checkpoint every: {config['checkpoint_every']} generations")
    print()
    
    # Validate configuration
    validate_input_range(
        config['input_range'],
        config['window_size'],
        config['programme_length']
    )
    
    # Create simulator
    simulator = NumbaSimulator(
        window=config['window_size'],
        max_steps=config['max_steps'],
        halt_thresh=config['halt_thresh']
    )
    
    # Create optimizer
    optimizer = GAOptimizer(
        population_size=config['population_size'],
        mutation_rate=0.03,
        programme_mutation_rate=0.08,
        elite_fraction=0.10,
        tournament_size=3,
        random_immigrant_rate=0.20
    )
    
    # Initialize population
    print("Initializing population...")
    optimizer.initialize_population(programme_length=config['programme_length'])
    
    # Create fitness function based on encoder choice
    print(f"Creating {args.encoder} encoder...")

    # Store the encoder for testing later
    test_encoder = None

    if args.encoder == 'em43':
        print("Using EM-4/3 positional encoding: [programme][BB][0^(input+1)][R][0...]")
        test_encoder = get_em43_encoder(use_numba=True)
        population_fitness_fn = create_em43_fitness_function(
            simulator, config['input_range'],
            use_continuous_fitness=config['use_continuous_fitness'],
            sparsity_penalty=config['sparsity_penalty'],
            task_type="doubling"
        )

    elif args.encoder == 'binary':
        print(f"Using binary encoding with {args.binary_bits} bits:")
        print(f"Example: 9 -> binary 1001 -> {args.binary_state}00{args.binary_state}")
        test_encoder = get_binary_encoder(
            input_state=args.binary_state,
            max_bits=args.binary_bits,
            use_numba=True
        )
        population_fitness_fn = create_binary_fitness_function(
            simulator, config['input_range'],
            input_state=args.binary_state,
            max_bits=args.binary_bits,
            use_continuous_fitness=config['use_continuous_fitness'],
            sparsity_penalty=config['sparsity_penalty'],
            task_type="doubling"
        )

    elif args.encoder == 'custom':
        print("Using custom example encoder with offset-based positioning")
        test_encoder = ExampleCustomEncoder(marker_state=1, offset=3)
        population_fitness_fn = create_custom_encoder_fitness_function(
            simulator,
            encoder_class=ExampleCustomEncoder,
            encoder_kwargs={'marker_state': 1, 'offset': 3},
            input_range=config['input_range'],
            use_continuous_fitness=config['use_continuous_fitness'],
            sparsity_penalty=config['sparsity_penalty'],
            task_type="doubling"
        )
    
    # Create validation function for early stopping
    def create_validation_function(encoder_type, encoder, simulator, input_range):
        """Create validation function that uses the SAME simulation path as training."""
        validation_inputs = list(range(input_range[0], min(input_range[0] + 10, input_range[1] + 1)))
        validation_targets = [2 * x for x in validation_inputs]

        def validation_fn(genome, sim):
            """Validate genome using the SAME simulation path as training."""
            if encoder_type == 'em43':
                # Use the SAME NumbaSimulator path as training
                outputs = sim.simulate_batch(genome, validation_inputs)
            else:
                # Use configurable simulator for other encoders
                from emergent_models.simulators.configurable_population_parallel import simulate_population_batch_with_encoder
                from emergent_models.rules.sanitization import sanitize_rule, sanitize_programme

                rules_array = np.array([sanitize_rule(genome.rule.get_rule_array())], dtype=np.uint8)
                programmes_array = np.array([sanitize_programme(genome.programme)], dtype=np.uint8)
                inputs_array = np.array(validation_inputs, dtype=np.int64)

                outputs_array = simulate_population_batch_with_encoder(
                    rules_array, programmes_array, inputs_array,
                    config['window_size'], config['max_steps'], config['halt_thresh'],
                    encoder
                )
                outputs = outputs_array[0].tolist()

            # Calculate accuracy
            correct = sum(1 for target, output in zip(validation_targets, outputs) if output == target)
            return correct / len(validation_targets)

        return validation_fn

    validation_fn = create_validation_function(args.encoder, test_encoder, simulator, config['input_range'])

    # Create trainer
    trainer = PopulationTrainer(optimizer)
    
    # Train
    print("Starting training...")
    if not config['disable_early_stopping']:
        print(f"Early stopping enabled: will stop if accuracy reaches {config['early_stopping_threshold']:.1%}")
    start_time = time.time()

    results = trainer.train(
        population_fitness_fn=population_fitness_fn,
        validation_fn=validation_fn,
        generations=config['generations'],
        early_stopping_threshold=config['early_stopping_threshold'],
        disable_early_stopping=config['disable_early_stopping'],
        checkpoint_every=config['checkpoint_every'],
        verbose=True
    )
    
    training_time = time.time() - start_time

    # Extract results
    best_genome = results['best_genome']
    best_fitness = results['best_fitness']
    history = results['history']
    generations_completed = results['generations_completed']

    print(f"\nTraining completed in {training_time:.2f} seconds")
    print(f"Generations completed: {generations_completed}/{config['generations']}")
    if generations_completed < config['generations'] and not config['disable_early_stopping']:
        print("🎉 Early stopping triggered - target accuracy reached!")
    print(f"Best fitness: {best_fitness:.4f}")
    print(f"Best genome programme: {best_genome.programme}")
    
    # Test the best genome using the SAME simulation path as training
    print(f"\nTesting best genome with {args.encoder} encoding:")
    test_inputs = list(range(config['input_range'][0], config['input_range'][1] + 1))

    # CRITICAL FIX: Use the same simulation path for testing as was used for training
    if args.encoder == 'em43':
        # For EM-4/3, use the SAME NumbaSimulator path as training
        print("Using NumbaSimulator.simulate_batch() (same as training)")
        test_outputs = simulator.simulate_batch(best_genome, test_inputs)
    else:
        # For other encoders, use configurable simulator with the correct encoder
        print(f"Using configurable simulator with {args.encoder} encoder")
        from emergent_models.simulators.configurable_population_parallel import simulate_population_batch_with_encoder
        from emergent_models.rules.sanitization import sanitize_rule, sanitize_programme

        # Prepare data for encoder-aware simulation
        rules_array = np.array([sanitize_rule(best_genome.rule.get_rule_array())], dtype=np.uint8)
        programmes_array = np.array([sanitize_programme(best_genome.programme)], dtype=np.uint8)
        inputs_array = np.array(test_inputs, dtype=np.int64)

        # Simulate with the correct encoder
        outputs_array = simulate_population_batch_with_encoder(
            rules_array, programmes_array, inputs_array,
            config['window_size'], config['max_steps'], config['halt_thresh'],
            test_encoder
        )
        test_outputs = outputs_array[0].tolist()  # Extract outputs for single genome
    
    print("Input -> Expected -> Actual -> Status")
    print("-" * 40)
    correct = 0
    for inp, out in zip(test_inputs, test_outputs):
        expected = 2 * inp
        status = "✓" if out == expected else "✗"
        if out == expected:
            correct += 1
        print(f"{inp:2d} -> {expected:2d} -> {out:2d} {status}")
    
    accuracy = correct / len(test_inputs)
    print(f"\nAccuracy: {accuracy:.2%} ({correct}/{len(test_inputs)})")
    
    # Save results
    results_dir = Path("results")
    results_dir.mkdir(exist_ok=True)
    
    filename = f"best_doubling_genome_{args.encoder}.json"
    from emergent_models.training.checkpointing import save_genome
    save_genome(best_genome, results_dir / filename)
    print(f"Saved best genome to {results_dir / filename}")
    
    # Training statistics
    training_stats = trainer.get_training_stats()
    print(f"\nTraining Statistics:")
    print(f"- Total generations: {training_stats['generations']}")
    print(f"- Best fitness achieved: {training_stats['best_fitness']:.4f}")
    print(f"- Final mean fitness: {training_stats['final_mean_fitness']:.4f}")
    print(f"- Average time per generation: {training_stats['avg_generation_time']:.2f}s")
    
    return best_genome, history


def compare_encoders():
    """Compare different encoders on the same task."""
    
    print("Comparing Different Encoders")
    print("=" * 40)
    
    # Common configuration
    config = {
        'population_size': 200,
        'generations': 15,
        'programme_length': 8,
        'window_size': 50,
        'max_steps': 100,
        'halt_thresh': 0.50,
        'input_range': (1, 10),
        'sparsity_penalty': 0.01,
        'use_continuous_fitness': False,
        'early_stopping_threshold': 1.0,
        'disable_early_stopping': False
    }
    
    encoders_to_test = [
        ('EM-4/3 Positional', 'em43', {}),
        ('Binary 6-bit', 'binary', {'input_state': 2, 'max_bits': 6}),
        ('Binary 8-bit', 'binary', {'input_state': 2, 'max_bits': 8}),
        ('Custom Offset', 'custom', {'encoder_class': ExampleCustomEncoder, 
                                    'encoder_kwargs': {'marker_state': 1, 'offset': 2}})
    ]
    
    results = {}
    
    for name, encoder_type, encoder_kwargs in encoders_to_test:
        print(f"\n--- Testing {name} ---")
        
        # Set random seed for fair comparison
        np.random.seed(42)
        
        # Create simulator and optimizer
        simulator = NumbaSimulator(
            window=config['window_size'],
            max_steps=config['max_steps'],
            halt_thresh=config['halt_thresh']
        )
        
        optimizer = GAOptimizer(population_size=config['population_size'])
        optimizer.initialize_population(programme_length=config['programme_length'])
        
        # Create fitness function
        if encoder_type == 'em43':
            fitness_fn = create_em43_fitness_function(simulator, config['input_range'], **encoder_kwargs)
        elif encoder_type == 'binary':
            fitness_fn = create_binary_fitness_function(simulator, config['input_range'], **encoder_kwargs)
        elif encoder_type == 'custom':
            fitness_fn = create_custom_encoder_fitness_function(simulator, **encoder_kwargs, input_range=config['input_range'])
        
        # Create validation function for this encoder
        def create_encoder_validation_fn(encoder_type, encoder_kwargs):
            validation_inputs = list(range(config['input_range'][0], config['input_range'][1] + 1))
            validation_targets = [2 * x for x in validation_inputs]

            def validation_fn(genome, sim):
                if encoder_type == 'em43':
                    outputs = sim.simulate_batch(genome, validation_inputs)
                else:
                    # Use configurable simulation for non-EM43 encoders
                    from emergent_models.simulators.configurable_population_parallel import simulate_population_batch_with_encoder
                    from emergent_models.rules.sanitization import sanitize_rule, sanitize_programme

                    if encoder_type == 'binary':
                        from emergent_models.encoders.binary_encoder import get_binary_encoder
                        encoder = get_binary_encoder(**encoder_kwargs)
                    elif encoder_type == 'custom':
                        encoder = encoder_kwargs['encoder_class'](**encoder_kwargs.get('encoder_kwargs', {}))

                    rules_array = np.array([sanitize_rule(genome.rule.get_rule_array())], dtype=np.uint8)
                    programmes_array = np.array([sanitize_programme(genome.programme)], dtype=np.uint8)
                    inputs_array = np.array(validation_inputs, dtype=np.int64)

                    outputs_array = simulate_population_batch_with_encoder(
                        rules_array, programmes_array, inputs_array,
                        config['window_size'], config['max_steps'], config['halt_thresh'],
                        encoder
                    )
                    outputs = outputs_array[0].tolist()

                correct = sum(1 for target, output in zip(validation_targets, outputs) if output == target)
                return correct / len(validation_targets)

            return validation_fn

        validation_fn = create_encoder_validation_fn(encoder_type, encoder_kwargs)

        # Train
        trainer = PopulationTrainer(optimizer)
        start_time = time.time()

        training_results = trainer.train(
            population_fitness_fn=fitness_fn,
            validation_fn=validation_fn,
            generations=config['generations'],
            early_stopping_threshold=config['early_stopping_threshold'],
            disable_early_stopping=config['disable_early_stopping'],
            verbose=False
        )
        
        training_time = time.time() - start_time
        
        # Test
        best_genome = training_results['best_genome']
        test_inputs = list(range(config['input_range'][0], config['input_range'][1] + 1))
        test_outputs = simulator.simulate_batch(best_genome, test_inputs)
        
        correct = sum(1 for inp, out in zip(test_inputs, test_outputs) if out == 2 * inp)
        accuracy = correct / len(test_inputs)
        
        generations_completed = training_results['generations_completed']
        early_stopped = generations_completed < config['generations']

        results[name] = {
            'fitness': training_results['best_fitness'],
            'accuracy': accuracy,
            'time': training_time,
            'generations': generations_completed,
            'early_stopped': early_stopped
        }

        print(f"Best fitness: {training_results['best_fitness']:.4f}")
        print(f"Accuracy: {accuracy:.1%}")
        print(f"Training time: {training_time:.2f}s")
        print(f"Generations: {generations_completed}/{config['generations']}")
        if early_stopped:
            print("🎉 Early stopping triggered!")
    
    # Summary
    print(f"\n{'='*15} COMPARISON SUMMARY {'='*15}")
    print(f"{'Encoder':<20} {'Fitness':<10} {'Accuracy':<10} {'Gens':<8} {'Time':<8} {'Early?':<8}")
    print("-" * 70)

    for name, result in results.items():
        early_mark = "✓" if result['early_stopped'] else ""
        print(f"{name:<20} {result['fitness']:<10.4f} {result['accuracy']:<10.1%} "
              f"{result['generations']:<8} {result['time']:<8.2f}s {early_mark:<8}")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "compare":
        compare_encoders()
    else:
        main()
